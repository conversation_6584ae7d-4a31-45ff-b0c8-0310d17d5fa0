<!-- @file 智能办公系统主界面 --> <!-- 文件标签：标记文件信息 -->
<!-- #region 文档头 --> <!-- 代码块标签：标记文档头区域 -->
<!DOCTYPE html> <!-- 定义文档类型为 HTML5 -->
<html lang="zh-CN"> <!-- 定义 HTML 文档的根元素，语言设置为简体中文 -->
<head> <!-- 定义文档的头部信息 -->
    <!-- 基础元数据配置 --> <!-- 注释：说明下方是基础元数据配置 -->
    <!-- #region meta配置 --> <!-- 代码块标签：标记 meta 配置区域 -->
    <meta charset="UTF-8"> <!-- 定义文档的字符编码为 UTF-8 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0"> <!-- 配置视口，用于响应式设计，设置宽度为设备宽度，初始缩放比例为 1.0 -->
    <title>SmartOffice - 智能办公系统</title> <!-- 定义浏览器标签页标题 -->
    <!-- #endregion meta配置 --> <!-- 代码块标签结束：meta 配置区域结束 -->
    <!-- #region 本地资源 --> <!-- 代码块标签：标记本地资源区域 -->
    <!-- 本地资源将通过JavaScript动态加载，实现完全离线运行 -->
    <!-- #endregion 本地资源 --> <!-- 代码块标签结束：本地资源区域结束 -->
    <link rel="stylesheet" href="css/template-image.css"> <!-- 引入模板图片相关的 CSS 样式文件 template-image.css -->
    <link rel="stylesheet" href="css/driver-agreement.css"> <!-- 引入司机协议相关的 CSS 样式文件 driver-agreement.css -->
    <link rel="stylesheet" href="css/print.css"> <!-- 引入打印专用样式文件 print.css -->
    <link rel="stylesheet" href="css/stamp-position.css"> <!-- 引入印章绝对定位样式文件 stamp-position.css -->
    <!-- 外部库将通过本地资源管理器动态加载 -->


    <!-- #region 核心样式 --> <!-- 代码块标签：标记核心样式区域 -->
    <link rel="stylesheet" href="css/core-styles.css"> <!-- 引入核心样式文件 core-styles.css -->
    <link rel="stylesheet" href="css/a4-template-optimization.css"> <!-- 引入A4纸张模板优化样式文件 -->


</head>
<body class="antialiased text-gray-800"> <!-- 设置页面主体样式：抗锯齿字体渲染和灰色文字 -->
    <div class="fixed inset-0 bg-blue-500 h-1.5"></div> <!-- 顶部固定蓝色进度条 -->

    <header class="bg-blue-600 text-white p-1">
        <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-xl font-bold">文档解析工具</h1>
            <!-- API状态指示器 -->
            <div id="api-status-indicator" class="text-sm bg-gray-700 text-gray-300 px-3 py-1 rounded-full flex items-center">
                <span class="flex h-2 w-2 relative mr-2">
                    <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-gray-400 opacity-75"></span>
                    <span class="relative inline-flex rounded-full h-2 w-2 bg-gray-500"></span>
                </span>
                <span class="api-status-text">API状态检查中...</span>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-6">
        <div class="flex flex-col lg:flex-row gap-6">
            <!-- Left Column (Configuration) - On mobile, collapses into panels --> <!-- 左侧栏目（配置）区域开始 -->
            <div class="w-full lg:w-1/4"> <!-- 左侧列开始：移动端占满全宽，大屏幕占1/4宽 -->
                <div class="bg-white rounded-lg shadow-md p-4 mb-4"> <!-- 卡片容器：白底，圆角，阴影，内边距4，下外边距4 -->
                    <div class="flex justify-between items-center cursor-pointer" data-target="company-panel"> <!-- 公司配置面板头：flex布局，两端对齐，可点击折叠 -->
                        <h2 class="text-lg font-semibold text-gray-700">公司配置</h2> <!-- 面板标题：lg字体，半粗体，灰色 -->
                        <i class="fas fa-chevron-down text-gray-400 lg:hidden"></i> <!-- 折叠图标：下箭头，移动端可见 -->
                    </div> <!-- 公司配置面板头结束 -->
                    <div class="collapsible-panel active" id="company-panel"> <!-- 可折叠面板内容开始：默认展开 -->
                        <div class="mt-4"> <!-- 配置项容器开始：顶部外边距4 -->
                            <label class="block text-sm font-medium text-gray-700 mb-1">选择公司</label> <!-- 标签：块级，sm字体，中等粗体，灰色，下外边距1 -->
                            <select id="company-selector" title="选择公司 / Select Company" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5"> <!-- 公司选择下拉框开始 -->
                                <option value="sky-mirror">Sky Mirror World Tour SDN BHD</option> <!-- 选项：Sky Mirror World Tour SDN BHD -->
                                <option value="gomyhire" selected>GoMyHire Travel SDN BHD</option> <!-- 选项：GoMyHire Travel SDN BHD -->
                            </select> <!-- 公司选择下拉框结束 -->
                        </div> <!-- 配置项容器结束 -->
                        <div class="mb-4">
                            <label for="document-type" class="block text-sm font-medium text-gray-700">文档类型 / Document Type</label>
                            <select id="document-type" name="document-type" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                <option value="receipt">收据 / Receipt</option>
                                <option value="invoice">发票 / Invoice</option>
                                <option value="quotation">报价单 / Quotation</option>
                                <option value="driver_agreement">驾驶协议 / Driver Agreement</option>
                            </select>
                        </div>
                        <div class="mt-4"> <!-- 货币类型容器开始：顶部外边距4 -->
                            <label class="block text-sm font-medium text-gray-700 mb-1">货币类型</label> <!-- 标签：块级，sm字体，中等粗体，灰色，下外边距1 -->
                            <select id="currency-type" title="选择货币类型 / Select Currency" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5"> <!-- 货币类型选择下拉框开始 -->
                                <option value="RM">令吉 (RM)</option> <!-- 选项：令吉 -->
                                <option value="RMB">人民币 (RMB)</option> <!-- 选项：人民币 -->
                            </select> <!-- 货币类型选择下拉框结束 -->
                        </div> <!-- 货币类型容器结束 -->

                        <!-- 签名选项 -->
                        <div class="mt-4">
                            <div class="flex items-center">
                                <input type="checkbox" id="show-signature" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                                <label for="show-signature" class="ml-2 text-sm font-medium text-gray-700">显示授权签名区域</label>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">勾选此项将在文档底部显示授权签名和日期区域</p>
                        </div>
                        <div class="mt-4"> <!-- 付款方式容器开始：顶部外边距4 -->
                            <label class="block text-sm font-medium text-gray-700 mb-1">付款方式</label> <!-- 标签：块级，sm字体，中等粗体，灰色，下外边距1 -->
                            <select id="payment-method" title="选择付款方式 / Select Payment Method" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5"> <!-- 付款方式选择下拉框开始 -->
                                <option value="online">线上支付 / Online Payment</option> <!-- 选项：线上支付 -->
                                <option value="bank">银行转账 / Bank Transfer</option> <!-- 选项：银行转账 -->
                                <option value="platform">平台付款 / Platform Payment</option> <!-- 选项：平台付款 -->
                            </select> <!-- 付款方式选择下拉框结束 -->
                        </div> <!-- 付款方式容器结束 -->
                    </div> <!-- 公司配置面板内容结束 -->
                </div> <!-- 卡片容器结束 -->
                
                <!-- Gemini API配置面板 -->
                <div class="bg-white rounded-lg shadow-md p-4 mb-4">
                    <div class="flex justify-between items-center cursor-pointer" data-target="gemini-config-panel">
                        <h2 class="text-lg font-semibold text-gray-700">智能分析配置</h2>
                        <i class="fas fa-chevron-down text-gray-400 lg:hidden"></i>
                    </div>
                    <div class="collapsible-panel active" id="gemini-config-panel">
                        <div class="mt-4">
                            <!-- NLP模式选择 -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">分析模式</label>
                                <select id="nlp-mode-selector" title="选择智能分析模式" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5">
                                    <option value="local">本地处理（离线）</option>
                                    <option value="gemini">Gemini AI（在线）</option>
                                </select>
                                <p class="text-xs text-gray-500 mt-1">本地模式无需网络，Gemini模式支持图片分析</p>
                            </div>
                            
                            <!-- Gemini API配置已硬植入，无需界面配置 -->
                        </div>
                    </div>
                </div>
            </div> <!-- 左侧列结束 -->
            <!-- Left Column (Configuration) - On mobile, collapses into panels --> <!-- 左侧栏目（配置）区域结束 -->

            <!-- Middle Column (Input & Edit) - 模块开始 -->
            <div class="w-full lg:w-1/3"> <!-- 中间列开始：移动端占满全宽，大屏幕占1/3宽 -->
                <div class="bg-white rounded-lg shadow-md p-4 mb-4"> <!-- 自然语言输入卡片开始 -->
                    <div class="flex justify-between items-center cursor-pointer" data-target="input-panel"> <!-- 输入面板头：flex布局，两端对齐，可点击折叠 -->
                        <h2 class="text-lg font-semibold text-gray-700">自然语言输入</h2> <!-- 面板标题：lg字体，半粗体，灰色 -->
                        <i class="fas fa-chevron-down text-gray-400 lg:hidden"></i> <!-- 折叠图标：下箭头，移动端可见 -->
                    </div> <!-- 输入面板头结束 -->
                    <div class="collapsible-panel active" id="input-panel"> <!-- 输入面板内容开始：默认展开 -->
                        <div class="mt-4"> <!-- 输入容器开始：顶部外边距4 -->
                            <textarea id="natural-language-input" rows="6" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="请输入自然语言描述，如：张三订购了2晚吉隆坡希尔顿酒店，单价300令吉，接机服务100令吉，合计700令吉。"></textarea> <!-- 文本输入框：多行，自适应宽度，边框圆角等 -->

                            <!-- 添加图片上传区域 -->
                            <div class="mt-3 flex flex-col">
                                <label class="flex items-center space-x-2 cursor-pointer p-2 bg-gray-50 border border-gray-300 rounded-lg">
                                    <i class="fas fa-file-image text-blue-600"></i>
                                    <span class="text-sm text-gray-700">上传图片分析</span>
                                    <input type="file" id="image-upload-input" class="hidden" accept="image/*">
                                </label>
                                <div id="image-preview-container" class="mt-2 hidden">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-10 h-10 bg-gray-200 rounded overflow-hidden flex items-center justify-center">
                                            <img id="image-preview" class="max-w-full max-h-full object-contain hidden" alt="上传的图片预览">
                                            <i id="image-preview-placeholder" class="fas fa-image text-gray-400"></i>
                                        </div>
                                        <span id="image-upload-filename" class="text-sm text-gray-600"></span>
                                    </div>
                                </div>
                            </div>
                            
                            <button type="button" id="parse-button" class="mt-3 w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg flex items-center justify-center"> <!-- 智能解析按钮开始 -->
                                <i class="fas fa-magic mr-2"></i> 智能解析 <!-- 按钮图标及文本 -->
                            </button> <!-- 智能解析按钮结束 -->
                        </div> <!-- 输入容器结束 -->
                    </div> <!-- 输入面板内容结束 -->
                </div> <!-- 自然语言输入卡片结束 -->
                <div class="bg-white rounded-lg shadow-md p-4 mb-4"> <!-- 文档编辑卡片开始 -->
                    <div class="flex justify-between items-center cursor-pointer" data-target="edit-panel"> <!-- 编辑面板头：flex布局，两端对齐可点击折叠 -->
                        <h2 class="text-lg font-semibold text-gray-700">文档编辑</h2> <!-- 面板标题：lg字体，半粗体，灰色 -->
                        <i class="fas fa-chevron-down text-gray-400 lg:hidden"></i> <!-- 折叠图标：下箭头，移动端可见 -->
                    </div> <!-- 编辑面板头结束 -->
                    <div class="collapsible-panel active" id="edit-panel"> <!-- 编辑面板内容开始：默认展开 -->
                        <div class="mt-4"> <!-- 编辑区域容器开始：顶部外边距4 -->
                            <!-- 单号和日期字段 --> <!-- 注释：单号日期字段开始 -->
                            <div class="grid grid-cols-2 gap-4 mb-4"> <!-- 表单网格：2列，间距4，底部外边距4 -->
                                <div>
                                    <label for="receipt-number-input" class="block text-sm font-medium text-gray-700 mb-1">单号 / No.</label> <!-- 标签：块级，sm字体，中等粗体，灰色，下外边距1 -->
                                    <input type="text" id="receipt-number-input" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="单号"> <!-- 单号输入框 -->
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">日期 / Date</label> <!-- 标签：块级，sm字体，中等粗体，灰色，下外边距1 -->
                                    <input type="date" id="receipt-date-input" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" aria-label="日期" title="选择日期"> <!-- 日期输入框 -->
                                </div>
                            </div>

                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">客户名称</label> <!-- 标签：块级，sm字体，中等粗体，灰色，下外边距1 -->
                                <input type="text" id="customer-name" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="客户名称" value="N/A"> <!-- 客户名称输入框 -->
                            </div>

                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">渠道</label> <!-- 标签：块级，sm字体，中等粗体，灰色，下外边距1 -->
                                <input type="text" id="channel" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5 mb-2" placeholder="渠道" value="N/A"> <!-- 渠道输入框, 增加 mb-2 -->
                                <select id="channel-preset-selector" title="选择预设渠道 / Select Preset Channel" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5">
                                    <option value="">-- 选择预设渠道 --</option>
                                    <option value="飞猪国际">飞猪国际</option>
                                    <option value="天空号国际">天空号国际</option>
                                    <option value="Whatsapp">Whatsapp</option>
                                    <option value="GoMyHire Webpage">GoMyHire Webpage</option>
                                    <option value="BNI Member">BNI Member</option>
                                    <option value="Wilson">Wilson</option>
                                    <option value="Wiracle">Wiracle</option>
                                    <option value="SMW Agent">SMW Agent</option>
                                    <option value="Travel Agency">Travel Agency</option>
                                    <option value="Other">其他 (手动输入)</option>
                                </select>
                            </div>

                            <!-- 买方信息折叠面板 -->
                            <div class="mt-4">
                                <div class="flex justify-between items-center cursor-pointer" data-target="buyer-panel"> <!-- 买方信息面板头：flex布局，两端对齐，可点击折叠 -->
                                    <h3 class="text-sm font-medium text-gray-700">买方信息</h3> <!-- 面板标题：sm字体，中等粗体，灰色 -->
                                    <i class="fas fa-chevron-down text-gray-400"></i> <!-- 折叠图标：下箭头 -->
                                </div>
                                <div class="collapsible-panel active" id="buyer-panel"> <!-- 可折叠面板内容开始：默认展开 -->
                                    <div class="mt-2 space-y-3">
                                        <div>
                                            <label class="block text-xs font-medium text-gray-700 mb-1">单位名称</label> <!-- 标签：块级，xs字体，中等粗体，灰色，下外边距1 -->
                                            <input type="text" id="buyer-company" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="单位名称" value="N/A"> <!-- 单位名称输入框 -->
                                        </div>
                                        <div>
                                            <label class="block text-xs font-medium text-gray-700 mb-1">税号</label> <!-- 标签：块级，xs字体，中等粗体，灰色，下外边距1 -->
                                            <input type="text" id="buyer-taxid" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="税号" value="N/A"> <!-- 税号输入框 -->
                                        </div>
                                        <div>
                                            <label class="block text-xs font-medium text-gray-700 mb-1">开户银行</label> <!-- 标签：块级，xs字体，中等粗体，灰色，下外边距1 -->
                                            <input type="text" id="buyer-bank" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="开户银行" value="N/A"> <!-- 开户银行输入框 -->
                                        </div>
                                        <div>
                                            <label class="block text-xs font-medium text-gray-700 mb-1">银行账户</label> <!-- 标签：块级，xs字体，中等粗体，灰色，下外边距1 -->
                                            <input type="text" id="buyer-account" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="银行账户" value="N/A"> <!-- 银行账户输入框 -->
                                        </div>
                                        <div>
                                            <label class="block text-xs font-medium text-gray-700 mb-1">联系电话</label> <!-- 标签：块级，xs字体，中等粗体，灰色，下外边距1 -->
                                            <input type="text" id="buyer-tel" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="联系电话" value="N/A"> <!-- 联系电话输入框 -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">服务项目</label> <!-- 标签：块级，sm字体，中等粗体，灰色，下外边距1 -->
                                <div id="service-items-container">
                                    <!-- Service items will be added here -->
                                    <!-- 修改占位项目结构，加入数量和单价 -->
                                    <div class="service-item mb-2 flex items-center space-x-1">
                                        <input type="text" class="service-description flex-grow bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-l-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="项目描述">
                                        <input type="number" class="service-quantity w-16 bg-gray-50 border border-gray-300 text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="数量" value="1" min="0">
                                        <input type="number" class="service-unit-price w-24 bg-gray-50 border border-gray-300 text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="单价" min="0" step="0.01">
                                        <input type="number" class="service-amount w-24 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-r-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="金额" min="0" step="0.01">
                                        <button type="button" class="remove-item ml-1 text-red-500 hover:text-red-700 p-2" title="删除项目">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <button type="button" id="add-service-item" class="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                                    <i class="fas fa-plus mr-1"></i> 添加项目
                                </button>
                            </div>

                            <div class="mb-4">
                                <div class="flex justify-between">
                                    <label class="block text-sm font-medium text-gray-700">总计金额</label>
                                    <span id="total-amount-display" class="text-lg font-bold text-gray-900">0.00</span>
                                </div>
                            </div>

                            <!-- 发票特定字段 -->
                            <div class="mt-4 hidden-panel" id="invoice-specific-panel">
                                <div class="border-t border-gray-200 pt-4">
                                    <h3 class="text-sm font-medium text-gray-700 mb-2">发票信息</h3>
                                    <div>
                                        <label class="block text-xs font-medium text-gray-700 mb-1">税率 (%)</label>
                                        <input type="number" id="tax-rate-input" step="0.01" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="例如: 6">
                                    </div>
                                     <!-- 可以在这里添加 Due Date 输入框 -->
                                     <!--
                                      <div>
                                        <label class="block text-xs font-medium text-gray-700 mb-1">到期日期</label>
                                        <input type="date" id="due-date-input" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5">
                                      </div>
                                     -->
                                </div>
                            </div>

                            <!-- 报价单特定字段 -->
                            <div class="mt-4 hidden-panel" id="quotation-specific-panel">
                                <div class="border-t border-gray-200 pt-4">
                                    <h3 class="text-sm font-medium text-gray-700 mb-2">报价单信息</h3>
                                    <div class="grid grid-cols-2 gap-4 mb-3">
                                        <div>
                                            <label for="valid-until-input" class="block text-xs font-medium text-gray-700 mb-1">有效期至</label>
                                            <input type="date" id="valid-until-input" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5">
                                        </div>
                                        <div>
                                            <label class="block text-xs font-medium text-gray-700 mb-1">折扣 (%)</label>
                                            <input type="number" id="discount-percentage-input" step="0.01" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="例如: 10">
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label for="prepared-by-input" class="block text-xs font-medium text-gray-700 mb-1">报价人</label>
                                            <input type="text" id="prepared-by-input" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="例如: 张三">
                                        </div>
                                        <div>
                                            <label for="estimated-delivery-input" class="block text-xs font-medium text-gray-700 mb-1">预计交付时间</label>
                                            <input type="date" id="estimated-delivery-input" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 司机协议功能已移至 driver_agreement.html -->

                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">备注信息</label>
                                <textarea id="notes" rows="2" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="额外备注信息（可选）"></textarea>
                            </div>

                            <!-- MOVED Form Conclusion Textarea HERE -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">结语</label>
                                <!-- 双语模式开关 -->
                                <div class="flex items-center mb-2">
                                    <input type="checkbox" id="bilingual-mode" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500" checked>
                                    <label for="bilingual-mode" class="ml-2 text-sm font-medium text-gray-700">双语模式（同时显示中英文）</label>
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <!-- 中文结语输入框 -->
                                    <div>
                                        <label class="block text-xs font-medium text-gray-500 mb-1">中文结语</label>
                                        <textarea id="conclusion-text-cn" rows="2" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="中文结语（可选）">感谢您的惠顾，期待再次为您服务！</textarea>
                                    </div>
                                    <!-- 英文结语输入框 -->
                                    <div>
                                        <label class="block text-xs font-medium text-gray-500 mb-1">英文结语</label>
                                        <textarea id="conclusion-text-en" rows="2" class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="English conclusion (optional)">Thank you for your business, we look forward to serving you again!</textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="button-group">
                                <button type="button" id="update-preview-button" class="preview-button">
                                    <i class="fas fa-sync-alt button-icon"></i> 更新预览
                                </button>
                                <button type="button" id="reset-form-button" class="reset-button">
                                    <i class="fas fa-undo button-icon"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column (Preview) -->
            <div class="w-full lg:w-1/2">
                <div class="bg-white rounded-lg shadow-md p-4 mb-4">
                    <div class="preview-header">
                        <h2 class="preview-title">文档预览</h2>
                        <div class="preview-controls">
                            <!-- 缩放控件 -->
                            <div class="zoom-controls">
                                <button type="button" id="zoom-out-button" class="zoom-button" title="缩小 / Zoom Out">
                                    <i class="fas fa-search-minus"></i>
                                </button>
                                <span id="zoom-level" class="zoom-level">100%</span>
                                <button type="button" id="zoom-in-button" class="zoom-button" title="放大 / Zoom In">
                                    <i class="fas fa-search-plus"></i>
                                </button>
                                <button type="button" id="zoom-reset-button" class="zoom-reset" title="重置 / Reset Zoom">
                                    <i class="fas fa-redo-alt"></i>
                                </button>
                            </div>
                            <button type="button" id="toggle-preview-button" class="toggle-preview" title="切换预览 / Toggle Preview">
                                <i class="fas fa-expand-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div id="preview-container" class="mt-4 preview-container">
                        <div class="flex justify-between items-center mb-2">
                            <h3 class="text-sm font-semibold text-gray-600">实时预览</h3>
                            <div class="flex items-center space-x-2">
                                <button id="print-range-toggle-btn" class="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200 transition-colors" title="显示/隐藏打印范围">
                                    <i class="fas fa-print mr-1"></i>打印范围
                                </button>
                                <button id="export-image-button" class="text-xs bg-green-100 text-green-600 px-2 py-1 rounded hover:bg-green-200 transition-colors" title="导出为图片">
                                    <i class="fas fa-image mr-1"></i>导出图片
                                </button>
                                <button id="export-pdf-button" class="text-xs bg-red-100 text-red-600 px-2 py-1 rounded hover:bg-red-200 transition-colors" title="导出为PDF">
                                    <i class="fas fa-file-pdf mr-1"></i>导出PDF
                                </button>
                                <div class="text-xs text-gray-500">自动更新</div>
                            </div>
                        </div>
                        <div id="document-preview" class="bg-white border border-gray-200 rounded-lg relative overflow-visible">
                            <!-- 文档容器 - 根据选择的模板样式应用不同的类 -->
                            <div id="document-container" class="relative bg-white rounded-lg shadow-lg overflow-visible template-modern">
                                <!-- 打印范围指示器 -->
                                <div id="print-range-indicator" class="print-range-indicator hidden">
                                    <!-- 打印边界线 -->
                                    <div class="print-boundary-lines">
                                        <div class="print-boundary-top"></div>
                                        <div class="print-boundary-bottom"></div>
                                        <div class="print-boundary-left"></div>
                                        <div class="print-boundary-right"></div>
                                    </div>
                                    
                                    <!-- 打印安全区域 -->
                                    <div class="print-safe-area"></div>
                                    
                                    <!-- 页面分割线 (如果需要) -->
                                    <div class="page-break-indicator page-break-a4"></div>
                                    
                                    <!-- 打印范围标签 -->
                                    <div class="print-range-labels">
                                        <div class="margin-label-top">上边距 20mm</div>
                                        <div class="margin-label-bottom">下边距 15mm</div>
                                        <div class="margin-label-left">左边距 10mm</div>
                                        <div class="margin-label-right">右边距 10mm</div>
                                        <div class="print-area-size-label">打印区域: 718×991px</div>
                                        <div class="page-info-label">A4 (210×297mm)</div>
                                    </div>
                                    
                                    <!-- 打印范围图例 -->
                                    <div class="print-range-legend">
                                        <div class="legend-item">
                                            <div class="legend-color boundary"></div>
                                            <span>打印边界</span>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-color margin"></div>
                                            <span>页边距</span>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-color safe-area"></div>
                                            <span>安全区域</span>
                                        </div>
                                        <div class="legend-item">
                                            <div class="legend-color page-break"></div>
                                            <span>分页线</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 页眉容器 - 绝对定位在document-container内部 -->
                                <div class="document-header document-header-image-container">
                                    <img id="company-logo" class="template-header-image" alt="Company Logo">
                                </div>
                                
                                <!-- 顶部彩色条纹 - 仅在现代模板中显示 -->
                                <div class="template-accent-bar"></div>
                                
                                <div class="main-content-wrapper"> <!-- START: 主内容伸缩包装器 -->
                                    <!-- 文档标题和编号区域 -->
                                    <div class="document-title-section p-6 text-center">
                                        <h1 id="document-title" class="text-2xl font-bold text-gray-800 mb-2 receipt-title">收据 / Receipt</h1>
                                        <h1 id="document-title-invoice" class="text-2xl font-bold text-gray-800 mb-2 invoice-title hidden">发票 / Invoice</h1>
                                        <h1 id="document-title-quotation" class="text-2xl font-bold text-gray-800 mb-2 quotation-title hidden">报价单 / Quotation</h1>
                                        <div class="document-number-container flex justify-center space-x-8">
                                            <!-- 收据编号 -->
                                            <p id="document-number" class="text-gray-600 hidden">单号 / No.: <span id="receipt-number" class="font-semibold">20230001</span></p>
                                            <!-- 发票编号 -->
                                            <p id="document-number-invoice" class="text-gray-600 hidden">发票号 / Invoice No.: <span id="invoice-number" class="font-semibold">INV-20230001</span></p>
                                            <!-- 报价单编号 -->
                                            <p id="document-number-quotation" class="text-gray-600 hidden">报价单号 / Quotation No.: <span id="quotation-number" class="font-semibold">QUO-20230001</span></p>
                                            <p id="document-date" class="text-gray-600 document-date">日期 / Date: <span id="current-date" class="font-semibold">2023-01-01</span></p>
                                        </div>
                                    </div>
                                <!-- 客户和买方信息区域 -->
                                <div class="client-section p-6 bg-gray-50">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <!-- 客户信息 -->
                                        <div class="customer-info">
                                            <h3 class="text-sm font-semibold text-gray-500 uppercase mb-2">客户信息 / Customer</h3>
                                            <div class="p-4 bg-white rounded-lg border border-gray-200">
                                                <p class="mb-2"><span class="font-medium">名称 / Name:</span> <span id="preview-customer-name" class="ml-2">--</span></p>
                                                <p><span class="font-medium">渠道 / Channel:</span> <span id="preview-channel" class="ml-2">--</span></p>
                                            </div>
                                        </div>

                                        <!-- 买方信息 -->
                                        <div class="buyer-info">
                                            <h3 class="text-sm font-semibold text-gray-500 uppercase mb-2">买方信息 / Buyer</h3>
                                            <div class="p-4 bg-white rounded-lg border border-gray-200">
                                                <p class="mb-1"><span class="font-medium">单位名称 / Company:</span> <span id="preview-buyer-company" class="ml-2">--</span></p>
                                                <p class="mb-1"><span class="font-medium">税号 / Tax ID:</span> <span id="preview-buyer-taxid" class="ml-2">--</span></p>
                                                <p class="mb-1"><span class="font-medium">开户银行 / Bank:</span> <span id="preview-buyer-bank" class="ml-2">--</span></p>
                                                <p class="mb-1"><span class="font-medium">银行账户 / Account:</span> <span id="preview-buyer-account" class="ml-2">--</span></p>
                                                <p><span class="font-medium">联系电话 / Tel:</span> <span id="preview-buyer-tel" class="ml-2">--</span></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 服务项目表格 -->
                                <div class="items-section p-6">
                                    <h3 class="text-sm font-semibold text-gray-500 uppercase mb-3">服务项目 / Service Items</h3>
                                    <div class="overflow-x-auto">
                                        <table class="w-full mb-4 border-collapse">
                                            <thead>
                                                <tr class="bg-gray-100">
                                                    <th class="py-3 px-4 text-left font-semibold text-gray-700 border-b-2 border-gray-300">项目描述 / Description</th>
                                                    <th class="py-3 px-4 text-right font-semibold text-gray-700 border-b-2 border-gray-300 amount-column">金额 / Amount</th>
                                                </tr>
                                            </thead>
                                            <tbody id="preview-items">
                                                <!-- 服务项目将在这里动态填充 -->
                                                <tr>
                                                    <td class="py-3 px-4 text-gray-500 border-b border-gray-200" colspan="2">-- 暂无数据 --</td>
                                                </tr>
                                            </tbody>
                                            <tfoot>
                                                <tr class="bg-gray-50">
                                                    <td class="py-3 px-4 text-right font-bold text-gray-700 border-t-2 border-gray-300">总计 / Total:</td>
                                                    <td class="py-3 px-4 text-right font-bold text-gray-800 border-t-2 border-gray-300"><span id="preview-currency">RM</span> <span id="preview-total-amount" class="text-lg">0.00</span></td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>

                                <!-- 详细信息区域 (备注、付款方式、签名) -->
                                <div class="details-section p-2">
                                    <!-- 备注和付款方式 - 左右布局 -->
                                    <div class="flex flex-col md:flex-row gap-3 mb-2">
                                        <!-- 备注 -->
                                        <div class="notes-section md:w-1/2">
                                            <h4 class="text-xs font-semibold text-gray-500 uppercase mb-1">备注 / Notes</h4>
                                            <p id="preview-notes" class="text-gray-600 text-xs break-words">--</p>
                                        </div>

                                        <!-- 付款方式 - 仅在收据模式下显示 -->
                                        <div class="payment-method-section md:w-1/2 receipt-only-element">
                                             <h4 class="text-xs font-semibold text-gray-500 uppercase mb-1">付款方式 / Payment Method</h4>
                                             <p id="preview-payment-method" class="text-gray-600 text-xs break-words">--</p>
                                        </div>
                                    </div>
                                    
                                    <!-- REMOVED Conclusion from here as it's placed later -->
                                    
                                    <!-- 授权签名区域 - 通栏 -->
                                    <div class="signature-section hidden mt-6 border-t pt-4"> <!-- 初始隐藏, 添加上边框和上内边距 -->
                                        <div class="grid grid-cols-2 gap-8 items-end">
                                            <div>
                                                <div class="signature-line"></div>
                                                <p class="text-sm text-gray-600 company-name-signature">Company Name</p>
                                                <p class="text-xs text-gray-500">Authorized Signature</p>
                                            </div>
                                            <div>
                                                <div class="signature-line"></div>
                                                <p class="text-sm text-gray-600">Date</p>
                                            </div>
                                        </div>
                                    </div>
                                </div> <!-- END: 主内容伸缩包装器 -->

                                <!-- 印章容器，确保在所有内容之上 -->
                                <div class="company-stamp min-h-[150px] p-0 text-right">
                                    <!-- 印章图片将由DocumentRenderer._updateCompanyStamp动态添加 -->
                                </div>
                                <!-- 结论区域 - 通用模板使用 -->
                                <div class="conclusion-section mb-2">
                                    <div class="flex flex-col gap-0">
                                        <p class="conclusion-text text-center text-xs text-gray-500 break-words px-2" style="white-space: pre-wrap !important; margin: 4px auto;"></p>
                                        <p class="conclusion-text text-center text-xs text-gray-500 break-words px-2" style="white-space: pre-wrap !important; margin: 4px auto;"></p>
                                    </div>
                                </div>
                                
                                <!-- 电子生成提示 - 通用模板使用 -->
                                <div class="electronic-generated-notice" style="width: 100%; margin: 15px 0; padding: 5px 0; font-size: 0.6rem; color: #666; text-align: center; border-top: 1px solid #ddd; background: #fff;">
                                    <p style="margin: 0; padding: 0;">此文件由电子生成，无需签名 / This document is electronically generated, no signature required</p>
                                </div>
                                
                                <!-- 页脚容器 - 绝对定位在document-container内部 -->
                                <div class="document-footer unified-document-footer company-footer-image-container border-t border-gray-200">
                                    <!-- 公司页脚图片 -->
                                    <img id="company-footer" class="template-footer-image" alt="Company Footer">
                                    <!-- 文档页脚内容 -->
                                    <div class="document-footer-content">
                                        <!-- 将由JS动态填充内容 -->
                                    </div>
                                </div>
                                <!-- 文档内容结束 -->
                            </div>

                            <!-- 司机协议专用容器 - 确保容器可见性由JS控制 -->
                            <div id="specific-driver-agreement-render-area" class="relative bg-white rounded-lg shadow-lg overflow-hidden driver-agreement-container hidden">
                                <!-- 司机协议内容区域 -->
                                <div id="driver-agreement-content" class="p-4">
                                    <!-- 内容将由DocumentRenderer.js动态生成 -->
                                    <div class="text-center">
                                        <p class="text-gray-400">司机协议内容加载中...</p>
                                    </div>
                                </div>
                                
                                <!-- 司机协议专用印章容器 -->
                                <div class="driver-agreement-stamp-container" style="position: absolute; top: 122mm; right: 20mm; width: 80px; height: 80px; z-index: 999;">
                                    <img id="specific-driver-agreement-stamp" alt="Company Stamp" style="width: 100%; height: 100%; object-fit: contain;">
                                </div>
                                
                                <!-- 司机协议专用电子生成提示 -->
                                <div class="driver-agreement-electronic-notice" style="width: 100%; margin: 15px 0; padding: 5px 0; font-size: 0.6rem; color: #666; text-align: center; border-top: 1px solid #ddd; background: #fff;">
                                    <p style="margin: 0; padding: 0;">此文件由电子生成，无需签名 / This document is electronically generated, no signature required</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Mobile Action Button -->
    <div class="fab md:hidden" id="mobile-action-button">
        <i class="fas fa-file-export"></i>
    </div>

    <!-- Mobile Action Menu (initially hidden) -->
    <div id="mobile-action-menu" class="fixed bottom-20 right-4 bg-white rounded-lg shadow-lg p-2 hidden">
        <div class="flex flex-col space-y-2">
            <button type="button" id="mobile-save" class="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center">
                <i class="fas fa-save mr-2"></i> 保存
            </button>
            <button type="button" id="mobile-export" class="bg-green-600 text-white px-4 py-2 rounded-lg flex items-center">
                <i class="fas fa-image mr-2"></i> 导出图片
            </button>
            <button type="button" id="mobile-pdf-export" class="bg-red-600 text-white px-4 py-2 rounded-lg flex items-center">
                <i class="fas fa-file-pdf mr-2"></i> 导出PDF
            </button>
        </div>
    </div>

    <!-- 引入统一架构 -->
    <!-- 首先加载资源文件 -->
    <script src="assets/image-base64.js"></script>

    <!-- 加载统一架构应用 -->
    <script type="module">
        // 统一架构应用启动脚本
        import { quickStartMain } from './modules/main.js';

        // 等待DOM加载完成后启动应用
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                console.log('🚀 启动SmartOffice 2.0 统一架构版本...');

                // 启动应用
                const app = await quickStartMain({
                    enableResourceFallback: true,
                    enablePerformanceMonitoring: true,
                    enableWorkflow: true,
                    mode: 'unified'
                });

                console.log('✅ SmartOffice 2.0 统一架构版本启动完成');
                console.log('应用信息:', app.getAppInfo());

            } catch (error) {
                console.error('❌ 应用启动失败:', error);

                // 显示错误信息
                document.body.innerHTML = `
                    <div style="padding: 20px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 20px; color: #721c24;">
                        <h2>❌ SmartOffice 2.0 启动失败</h2>
                        <p><strong>错误原因:</strong> ${error.message}</p>
                        <p><strong>可能的解决方案:</strong></p>
                        <ul>
                            <li>检查网络连接</li>
                            <li>刷新页面重试</li>
                            <li>检查浏览器控制台获取详细错误信息</li>
                            <li>确保浏览器支持ES6模块</li>
                        </ul>
                        <button onclick="location.reload()" style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-top: 10px;">
                            重新加载页面
                        </button>
                    </div>
                `;
            }
        });

    </script>
</body>
</html>




    <!-- 引入新的模块化JavaScript文件 -->
    <script type="module" src="modules/main.js"></script>
    <script>
        // 初始化应用
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                console.log('🔄 开始初始化SmartOffice 2.0应用...');

                // 导入主应用模块
                const { createSmartOfficeMainApp } = await import('./modules/main.js');

                // 创建并启动应用
                const app = createSmartOfficeMainApp({
                    version: '2.0.0',
                    mode: 'production',
                    enableGemini: true,
                    geminiApiKey: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s'
                });

                await app.start();

                // 将应用实例挂载到全局
                window.smartOfficeApp = app;

                console.log('✅ SmartOffice 2.0应用初始化完成');

            } catch (error) {
                console.error('❌ SmartOffice 2.0应用初始化失败:', error);
            }
        });
    </script>









                    
                    if (testResult && testResult.confidence > 0) {
                        updateApiStatus('connected');
                        showSuccessNotification('连接成功', 'Gemini API连接正常，可以正常使用');
                        pageLogger.info('IndexPage', 'testGeminiConnection', '✅ Gemini API连接测试成功', {
                            confidence: testResult.confidence,
                            extractedFields: Object.keys(testResult.extractedData || {}).length
                        });
                    } else {
                        throw new Error('API响应无效');
                    }
                } else {
                    throw new Error('NLP管理器未初始化');
                }
                
            } catch (error) {
                updateApiStatus('error');
                showErrorNotification('连接失败', `Gemini API连接失败：${error.message}`);
                pageLogger.error('IndexPage', 'testGeminiConnection', '❌ Gemini API连接测试失败', {
                    error: error.message
                });
            }
        }
        
        /**
         * 更新NLP配置（硬植入API密钥版本）
         * @function updateNLPConfig
         */
        function updateNLPConfig() {
            const mode = document.getElementById('nlp-mode-selector')?.value;
            // API密钥已硬植入
            const apiKey = 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s';
            
            const config = {
                mode: mode || 'gemini',
                apiKey: apiKey,
                enableImageProcessing: true,
                enableMultiModal: true
            };
            
            // 更新应用配置
            if (window.smartOfficeApp && window.smartOfficeApp.getManager('offlineConfig')) {
                const configManager = window.smartOfficeApp.getManager('offlineConfig');
                configManager.set('nlp', config);
                
                pageLogger.debug('IndexPage', 'updateNLPConfig', 'NLP配置已更新（硬植入模式）', config);
            }
            
            // 🔧 修复：实际设置NLP管理器的API密钥
            if (window.smartOfficeApp && window.smartOfficeApp.getManager('nlp')) {
                const nlpManager = window.smartOfficeApp.getManager('nlp');
                try {
                    nlpManager.setApiKey(apiKey);
                    pageLogger.info('IndexPage', 'updateNLPConfig', '✅ NLP管理器API密钥已设置', { apiKeyLength: apiKey.length });
                } catch (error) {
                    pageLogger.error('IndexPage', 'updateNLPConfig', '❌ NLP管理器API密钥设置失败', { error: error.message });
                }
            } else {
                pageLogger.warn('IndexPage', 'updateNLPConfig', '⚠️ NLP管理器未找到，无法设置API密钥');
            }
            
            // 存储到本地存储
            try {
                localStorage.setItem('smartoffice_nlp_config', JSON.stringify(config));
            } catch (error) {
                pageLogger.warn('IndexPage', 'updateNLPConfig', '配置保存到本地存储失败', { error: error.message });
            }
        }
        
        /**
         * 显示成功通知
         * @function showSuccessNotification
         * @param {string} title - 通知标题
         * @param {string} message - 通知消息
         */
        function showSuccessNotification(title, message) {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white p-4 rounded-lg shadow-lg z-50 flex items-center max-w-sm';
            notification.innerHTML = `
                <i class="fas fa-check-circle mr-3 text-xl"></i>
                <div>
                    <h4 class="font-bold">${title}</h4>
                    <p class="text-sm">${message}</p>
                </div>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;
            
            document.body.appendChild(notification);
            
            // 自动移除通知
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
            
                         pageLogger.trace('IndexPage', 'showSuccessNotification', '成功通知已显示');
         }
         
         /**
          * 显示警告通知
          * @function showWarningNotification
          * @param {string} title - 通知标题
          * @param {string} message - 通知消息
          */
         function showWarningNotification(title, message) {
             const notification = document.createElement('div');
             notification.className = 'fixed top-4 right-4 bg-yellow-500 text-white p-4 rounded-lg shadow-lg z-50 flex items-center max-w-sm';
             notification.innerHTML = `
                 <i class="fas fa-exclamation-triangle mr-3 text-xl"></i>
                 <div>
                     <h4 class="font-bold">${title}</h4>
                     <p class="text-sm">${message}</p>
                 </div>
                 <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.remove()">
                     <i class="fas fa-times"></i>
                 </button>
             `;
             
             document.body.appendChild(notification);
             
             // 自动移除通知
             setTimeout(() => {
                 if (notification.parentElement) {
                     notification.remove();
                 }
             }, 6000); // 警告通知显示时间稍长
             
             pageLogger.trace('IndexPage', 'showWarningNotification', '警告通知已显示');
         }
         
                 /**
         * 从NLP结果填充表单字段
         * @function fillFormFromNLPResult
         * @param {Object} extractedData - 提取的数据
         */
        function fillFormFromNLPResult(extractedData) {
            pageLogger.debug('IndexPage', 'fillFormFromNLPResult', '开始填充表单字段', {
                fieldsCount: Object.keys(extractedData).length,
                extractedData: extractedData
            });
            
            try {
                let filledFields = 0;
                
                // 填充客户名称
                if (extractedData.customerName || extractedData.clientName || extractedData.buyerName || extractedData.driverName) {
                    const customerName = extractedData.customerName || extractedData.clientName || extractedData.buyerName || extractedData.driverName;
                    const customerNameInput = document.getElementById('customer-name');
                    if (customerNameInput) {
                        customerNameInput.value = customerName;
                        // 触发change和input事件，确保UI更新
                        customerNameInput.dispatchEvent(new Event('change', { bubbles: true }));
                        customerNameInput.dispatchEvent(new Event('input', { bubbles: true }));
                        filledFields++;
                        pageLogger.debug('IndexPage', 'fillFormFromNLPResult', '✅ 客户名称已填充', { 
                            customerName,
                            currentValue: customerNameInput.value 
                        });
                    } else {
                        pageLogger.warn('IndexPage', 'fillFormFromNLPResult', '❌ 客户名称输入框未找到');
                    }
                }
                
                // 填充单号
                if (extractedData.receiptNumber || extractedData.invoiceNumber || extractedData.quotationNumber || extractedData.agreementNumber) {
                    const number = extractedData.receiptNumber || extractedData.invoiceNumber || extractedData.quotationNumber || extractedData.agreementNumber;
                    const numberInput = document.getElementById('receipt-number-input');
                    if (numberInput) {
                        numberInput.value = number;
                        filledFields++;
                        pageLogger.debug('IndexPage', 'fillFormFromNLPResult', '✅ 单号已填充', { number });
                    } else {
                        pageLogger.warn('IndexPage', 'fillFormFromNLPResult', '❌ 单号输入框未找到');
                    }
                }
                
                // 填充日期
                if (extractedData.issueDate || extractedData.quotationDate || extractedData.signingDate || extractedData.date) {
                    const date = extractedData.issueDate || extractedData.quotationDate || extractedData.signingDate || extractedData.date;
                    const dateInput = document.getElementById('receipt-date-input');
                    if (dateInput && date) {
                        // 尝试解析日期格式
                        let dateValue = date;
                        if (typeof date === 'string') {
                            const parsedDate = new Date(date);
                            if (!isNaN(parsedDate.getTime())) {
                                dateValue = parsedDate.toISOString().split('T')[0];
                            }
                        }
                        dateInput.value = dateValue;
                        filledFields++;
                        pageLogger.debug('IndexPage', 'fillFormFromNLPResult', '✅ 日期已填充', { date: dateValue });
                    } else {
                        pageLogger.warn('IndexPage', 'fillFormFromNLPResult', '❌ 日期输入框未找到或日期无效', { date, hasInput: !!dateInput });
                    }
                }
                
                // 填充服务项目
                if (extractedData.services || extractedData.items) {
                    const services = extractedData.services || extractedData.items;
                    if (Array.isArray(services) && services.length > 0) {
                        fillServiceItems(services, extractedData);
                        filledFields++;
                        pageLogger.debug('IndexPage', 'fillFormFromNLPResult', '✅ 服务项目已填充', { servicesCount: services.length });
                    }
                } else if (extractedData.description) {
                    // 如果没有服务数组，但有描述，创建一个服务项目
                    const services = [extractedData.description];
                    fillServiceItems(services, extractedData);
                    filledFields++;
                    pageLogger.debug('IndexPage', 'fillFormFromNLPResult', '✅ 从描述创建服务项目', { description: extractedData.description });
                }
                
                // 填充总金额
                if (extractedData.totalAmount || extractedData.estimatedAmount || extractedData.amount) {
                    const amount = extractedData.totalAmount || extractedData.estimatedAmount || extractedData.amount;
                    const numericAmount = parseFloat(amount) || 0;
                    if (numericAmount > 0) {
                        updateTotalAmount(numericAmount);
                        filledFields++;
                        pageLogger.debug('IndexPage', 'fillFormFromNLPResult', '✅ 总金额已填充', { amount: numericAmount });
                    }
                }
                
                // 填充电话号码
                if (extractedData.phones && Array.isArray(extractedData.phones) && extractedData.phones.length > 0) {
                    const buyerTelInput = document.getElementById('buyer-tel');
                    if (buyerTelInput) {
                        buyerTelInput.value = extractedData.phones[0];
                        buyerTelInput.dispatchEvent(new Event('change', { bubbles: true }));
                        buyerTelInput.dispatchEvent(new Event('input', { bubbles: true }));
                        filledFields++;
                        pageLogger.debug('IndexPage', 'fillFormFromNLPResult', '✅ 电话号码已填充', { 
                            phone: extractedData.phones[0],
                            currentValue: buyerTelInput.value 
                        });
                    }
                } else if (extractedData.phone) {
                    const buyerTelInput = document.getElementById('buyer-tel');
                    if (buyerTelInput) {
                        buyerTelInput.value = extractedData.phone;
                        buyerTelInput.dispatchEvent(new Event('change', { bubbles: true }));
                        buyerTelInput.dispatchEvent(new Event('input', { bubbles: true }));
                        filledFields++;
                        pageLogger.debug('IndexPage', 'fillFormFromNLPResult', '✅ 电话号码已填充', { 
                            phone: extractedData.phone,
                            currentValue: buyerTelInput.value 
                        });
                    }
                }
                
                // 填充渠道信息
                if (extractedData.channel) {
                    const channelInput = document.getElementById('channel');
                    if (channelInput) {
                        channelInput.value = extractedData.channel;
                        filledFields++;
                        pageLogger.debug('IndexPage', 'fillFormFromNLPResult', '✅ 渠道信息已填充', { channel: extractedData.channel });
                    }
                }
                
                // 填充备注信息
                if (extractedData.notes) {
                    const notesInput = document.getElementById('notes');
                    if (notesInput) {
                        notesInput.value = extractedData.notes;
                        filledFields++;
                        pageLogger.debug('IndexPage', 'fillFormFromNLPResult', '✅ 备注信息已填充', { notes: extractedData.notes });
                    }
                }
                
                // 根据文档类型填充特定字段
                const documentType = document.getElementById('document-type')?.value;
                fillDocumentSpecificFields(extractedData, documentType);
                
                // 触发表单字段的change事件，确保UI更新
                const customerNameInput = document.getElementById('customer-name');
                if (customerNameInput) {
                    customerNameInput.dispatchEvent(new Event('change', { bubbles: true }));
                    customerNameInput.dispatchEvent(new Event('input', { bubbles: true }));
                }
                
                // 强制更新预览（如果有预览更新函数）
                if (typeof updatePreview === 'function') {
                    updatePreview();
                } else if (typeof updateDocumentPreview === 'function') {
                    updateDocumentPreview();
                }
                
                pageLogger.info('IndexPage', 'fillFormFromNLPResult', '✅ 表单字段填充完成', {
                    filledFields,
                    totalFields: Object.keys(extractedData).length,
                    documentType
                });
                
                // 如果没有填充任何字段，显示警告
                if (filledFields === 0) {
                    pageLogger.warn('IndexPage', 'fillFormFromNLPResult', '⚠️ 没有填充任何表单字段', { extractedData });
                    showWarningNotification('填充提醒', '解析成功但未找到可填充的字段，请检查输入内容');
                } else {
                    // 显示成功填充的通知
                    showSuccessNotification('字段填充成功', `已成功填充 ${filledFields} 个字段`);
                }
                
            } catch (error) {
                pageLogger.error('IndexPage', 'fillFormFromNLPResult', '❌ 表单填充失败', { 
                    error: error.message,
                    stack: error.stack,
                    extractedData 
                });
                showErrorNotification('填充失败', `表单填充时发生错误：${error.message}`);
            }
        }
         
         /**
          * 填充服务项目
          * @function fillServiceItems
          * @param {Array} services - 服务项目数组
          * @param {Object} extractedData - 完整的提取数据
          */
         function fillServiceItems(services, extractedData) {
             const container = document.getElementById('service-items-container');
             if (!container) return;
             
             // 清空现有项目
             container.innerHTML = '';
             
             const amounts = extractedData.amounts || [];
             const quantities = extractedData.quantities || [];
             
             services.forEach((service, index) => {
                 const serviceItem = document.createElement('div');
                 serviceItem.className = 'service-item mb-2 flex items-center space-x-1';
                 
                 const quantity = quantities[index] || 1;
                 const amount = amounts[index] || 0;
                 const unitPrice = amount / quantity || 0;
                 
                 serviceItem.innerHTML = `
                     <input type="text" class="service-description flex-grow bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-l-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="项目描述" value="${service}">
                     <input type="number" class="service-quantity w-16 bg-gray-50 border border-gray-300 text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="数量" value="${quantity}" min="0">
                     <input type="number" class="service-unit-price w-24 bg-gray-50 border border-gray-300 text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="单价" value="${unitPrice.toFixed(2)}" min="0" step="0.01">
                     <input type="number" class="service-amount w-24 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-r-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="金额" value="${amount.toFixed(2)}" min="0" step="0.01">
                     <button type="button" class="remove-item ml-1 text-red-500 hover:text-red-700 p-2" title="删除项目">
                         <i class="fas fa-times"></i>
                     </button>
                 `;
                 
                 container.appendChild(serviceItem);
                 
                 // 添加删除事件
                 const removeBtn = serviceItem.querySelector('.remove-item');
                 removeBtn.addEventListener('click', () => {
                     serviceItem.remove();
                     updateTotalAmount();
                 });
                 
                 // 添加计算事件
                 const quantityInput = serviceItem.querySelector('.service-quantity');
                 const unitPriceInput = serviceItem.querySelector('.service-unit-price');
                 const amountInput = serviceItem.querySelector('.service-amount');
                 
                 [quantityInput, unitPriceInput].forEach(input => {
                     input.addEventListener('input', () => {
                         const qty = parseFloat(quantityInput.value) || 0;
                         const price = parseFloat(unitPriceInput.value) || 0;
                         amountInput.value = (qty * price).toFixed(2);
                         updateTotalAmount();
                     });
                 });
             });
             
             // 更新总金额
             updateTotalAmount();
         }
         
         /**
          * 填充文档特定字段
          * @function fillDocumentSpecificFields
          * @param {Object} extractedData - 提取的数据
          * @param {string} documentType - 文档类型
          */
         function fillDocumentSpecificFields(extractedData, documentType) {
             switch (documentType) {
                 case 'invoice':
                     // 填充发票特定字段
                     if (extractedData.invoiceNumber) {
                         const invoiceNumberInput = document.getElementById('receipt-number-input');
                         if (invoiceNumberInput) {
                             invoiceNumberInput.value = extractedData.invoiceNumber;
                         }
                     }
                     if (extractedData.taxRate) {
                         const taxRateInput = document.getElementById('tax-rate-input');
                         if (taxRateInput) {
                             taxRateInput.value = extractedData.taxRate;
                         }
                     }
                     break;
                     
                 case 'quotation':
                     // 填充报价单特定字段
                     if (extractedData.quotationNumber) {
                         const quotationNumberInput = document.getElementById('receipt-number-input');
                         if (quotationNumberInput) {
                             quotationNumberInput.value = extractedData.quotationNumber;
                         }
                     }
                     if (extractedData.validUntil) {
                         const validUntilInput = document.getElementById('valid-until-input');
                         if (validUntilInput) {
                             const validDate = new Date(extractedData.validUntil);
                             if (!isNaN(validDate.getTime())) {
                                 validUntilInput.value = validDate.toISOString().split('T')[0];
                             }
                         }
                     }
                     break;
                     
                 case 'driver_agreement':
                     // 填充司机协议特定字段
                     if (extractedData.agreementNumber) {
                         const agreementNumberInput = document.getElementById('receipt-number-input');
                         if (agreementNumberInput) {
                             agreementNumberInput.value = extractedData.agreementNumber;
                         }
                     }
                     if (extractedData.driverPhone) {
                         const buyerTelInput = document.getElementById('buyer-tel');
                         if (buyerTelInput) {
                             buyerTelInput.value = extractedData.driverPhone;
                         }
                     }
                     break;
                     
                 default:
                     // 收据默认处理
                     if (extractedData.receiptNumber) {
                         const receiptNumberInput = document.getElementById('receipt-number-input');
                         if (receiptNumberInput) {
                             receiptNumberInput.value = extractedData.receiptNumber;
                         }
                     }
                     break;
             }
         }
         
                 /**
         * 更新总金额显示
         * @function updateTotalAmount
         * @param {number} amount - 指定金额（可选）
         */
        function updateTotalAmount(amount = null) {
            const totalDisplay = document.getElementById('total-amount-display');
            if (!totalDisplay) return;
            
            let total = amount;
            
            if (total === null) {
                // 计算所有服务项目的总金额
                total = 0;
                const serviceAmounts = document.querySelectorAll('.service-amount');
                serviceAmounts.forEach(input => {
                    total += parseFloat(input.value) || 0;
                });
            }
            
            totalDisplay.textContent = total.toFixed(2);
        }
        
        /**
         * 内联资源管理器 - 用于file://协议环境
         * @function initializeInlineResourceManager
         */
        async function initializeInlineResourceManager() {
            pageLogger.info('IndexPage', 'initializeInlineResourceManager', '🔄 初始化内联资源管理器');
            
            // 创建简化的资源管理器
            const inlineResourceManager = {
                loadedResources: new Set(),
                
                async loadAllResources() {
                    pageLogger.info('InlineResourceManager', 'loadAllResources', '🔄 开始加载内联资源');
                    
                    // 加载Tailwind CSS fallback
                    this.loadTailwindFallback();
                    
                    // 加载Font Awesome fallback
                    this.loadFontAwesomeFallback();
                    
                    // 加载html2canvas fallback
                    this.loadHtml2CanvasFallback();
                    
                    // 加载jsPDF fallback
                    this.loadJsPDFFallback();
                    
                    pageLogger.info('InlineResourceManager', 'loadAllResources', '✅ 内联资源加载完成');
                },
                
                loadTailwindFallback() {
                    if (this.loadedResources.has('tailwind')) return;
                    
                    const css = `
                        /* Tailwind CSS 核心样式 - 简化版本 */
                        .container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
                        .flex { display: flex; }
                        .flex-col { flex-direction: column; }
                        .flex-row { flex-direction: row; }
                        .items-center { align-items: center; }
                        .justify-between { justify-content: space-between; }
                        .justify-center { justify-content: center; }
                        .space-x-2 > * + * { margin-left: 0.5rem; }
                        .space-x-4 > * + * { margin-left: 1rem; }
                        .space-y-2 > * + * { margin-top: 0.5rem; }
                        .space-y-3 > * + * { margin-top: 0.75rem; }
                        .space-y-4 > * + * { margin-top: 1rem; }
                        .w-full { width: 100%; }
                        .w-1\\/2 { width: 50%; }
                        .w-1\\/3 { width: 33.333333%; }
                        .w-1\\/4 { width: 25%; }
                        .h-full { height: 100%; }
                        .p-2 { padding: 0.5rem; }
                        .p-4 { padding: 1rem; }
                        .p-6 { padding: 1.5rem; }
                        .px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
                        .px-4 { padding-left: 1rem; padding-right: 1rem; }
                        .py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
                        .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
                        .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
                        .m-2 { margin: 0.5rem; }
                        .m-4 { margin: 1rem; }
                        .mb-2 { margin-bottom: 0.5rem; }
                        .mb-4 { margin-bottom: 1rem; }
                        .mt-2 { margin-top: 0.5rem; }
                        .mt-4 { margin-top: 1rem; }
                        .ml-2 { margin-left: 0.5rem; }
                        .mr-2 { margin-right: 0.5rem; }
                        .text-sm { font-size: 0.875rem; }
                        .text-lg { font-size: 1.125rem; }
                        .text-xl { font-size: 1.25rem; }
                        .text-2xl { font-size: 1.5rem; }
                        .font-medium { font-weight: 500; }
                        .font-semibold { font-weight: 600; }
                        .font-bold { font-weight: 700; }
                        .text-white { color: #ffffff; }
                        .text-gray-500 { color: #6b7280; }
                        .text-gray-600 { color: #4b5563; }
                        .text-gray-700 { color: #374151; }
                        .text-gray-800 { color: #1f2937; }
                        .text-blue-600 { color: #2563eb; }
                        .text-red-500 { color: #ef4444; }
                        .text-green-600 { color: #16a34a; }
                        .bg-white { background-color: #ffffff; }
                        .bg-gray-50 { background-color: #f9fafb; }
                        .bg-gray-100 { background-color: #f3f4f6; }
                        .bg-blue-600 { background-color: #2563eb; }
                        .bg-blue-700 { background-color: #1d4ed8; }
                        .bg-green-500 { background-color: #22c55e; }
                        .bg-red-500 { background-color: #ef4444; }
                        .border { border-width: 1px; }
                        .border-gray-200 { border-color: #e5e7eb; }
                        .border-gray-300 { border-color: #d1d5db; }
                        .rounded { border-radius: 0.25rem; }
                        .rounded-lg { border-radius: 0.5rem; }
                        .shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); }
                        .shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); }
                        .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
                        .hidden { display: none; }
                        .block { display: block; }
                        .fixed { position: fixed; }
                        .relative { position: relative; }
                        .absolute { position: absolute; }
                        .top-4 { top: 1rem; }
                        .right-4 { right: 1rem; }
                        .z-50 { z-index: 50; }
                        .cursor-pointer { cursor: pointer; }
                        .hover\\:bg-blue-700:hover { background-color: #1d4ed8; }
                        .hover\\:text-blue-800:hover { color: #1e40af; }
                        .focus\\:ring-blue-500:focus { --tw-ring-color: #3b82f6; }
                        .focus\\:border-blue-500:focus { border-color: #3b82f6; }
                        .grid { display: grid; }
                        .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
                        .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
                        .gap-4 { gap: 1rem; }
                        .gap-6 { gap: 1.5rem; }
                        .overflow-hidden { overflow: hidden; }
                        .overflow-x-auto { overflow-x: auto; }
                        .antialiased { -webkit-font-smoothing: antialiased; }
                        @media (min-width: 1024px) {
                            .lg\\:w-1\\/4 { width: 25%; }
                            .lg\\:w-1\\/3 { width: 33.333333%; }
                            .lg\\:w-1\\/2 { width: 50%; }
                            .lg\\:flex-row { flex-direction: row; }
                            .lg\\:hidden { display: none; }
                        }
                        @media (min-width: 768px) {
                            .md\\:w-1\\/2 { width: 50%; }
                            .md\\:flex-row { flex-direction: row; }
                            .md\\:hidden { display: none; }
                        }
                    `;
                    
                    this.injectCSS(css, 'tailwind-fallback');
                    this.loadedResources.add('tailwind');
                    pageLogger.debug('InlineResourceManager', 'loadTailwindFallback', '✅ Tailwind CSS fallback已加载');
                },
                
                loadFontAwesomeFallback() {
                    if (this.loadedResources.has('fontawesome')) return;
                    
                    const css = `
                        /* Font Awesome 核心图标 - 简化版本 */
                        .fas, .fa { font-family: "Font Awesome 5 Free"; font-weight: 900; }
                        .fa-magic:before { content: "\\f0d0"; }
                        .fa-sync-alt:before { content: "\\f2f1"; }
                        .fa-undo:before { content: "\\f0e2"; }
                        .fa-search-minus:before { content: "\\f010"; }
                        .fa-search-plus:before { content: "\\f00e"; }
                        .fa-redo-alt:before { content: "\\f2f9"; }
                        .fa-expand-alt:before { content: "\\f424"; }
                        .fa-file-export:before { content: "\\f56e"; }
                        .fa-save:before { content: "\\f0c7"; }
                        .fa-image:before { content: "\\f03e"; }
                        .fa-file-pdf:before { content: "\\f1c1"; }
                        .fa-chevron-down:before { content: "\\f078"; }
                        .fa-plus:before { content: "\\f067"; }
                        .fa-times:before { content: "\\f00d"; }
                        .fa-eye:before { content: "\\f06e"; }
                        .fa-eye-slash:before { content: "\\f070"; }
                        .fa-flask:before { content: "\\f0c3"; }
                        .fa-file-image:before { content: "\\f1c5"; }
                        .fa-spinner:before { content: "\\f110"; }
                        .fa-spin { animation: fa-spin 2s infinite linear; }
                        .fa-exclamation-triangle:before { content: "\\f071"; }
                        .fa-check-circle:before { content: "\\f058"; }
                        @keyframes fa-spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                        }
                        /* 使用系统字体作为fallback */
                        .fas, .fa { 
                            font-family: system-ui, -apple-system, sans-serif;
                            font-style: normal;
                            font-variant: normal;
                            text-rendering: auto;
                            line-height: 1;
                        }
                        /* 简单的图标替代 */
                        .fa-magic:before { content: "✨"; }
                        .fa-sync-alt:before { content: "🔄"; }
                        .fa-undo:before { content: "↶"; }
                        .fa-search-minus:before { content: "🔍-"; }
                        .fa-search-plus:before { content: "🔍+"; }
                        .fa-redo-alt:before { content: "↷"; }
                        .fa-expand-alt:before { content: "⛶"; }
                        .fa-file-export:before { content: "📤"; }
                        .fa-save:before { content: "💾"; }
                        .fa-image:before { content: "🖼️"; }
                        .fa-file-pdf:before { content: "📄"; }
                        .fa-chevron-down:before { content: "▼"; }
                        .fa-plus:before { content: "+"; }
                        .fa-times:before { content: "×"; }
                        .fa-eye:before { content: "👁️"; }
                        .fa-eye-slash:before { content: "🙈"; }
                        .fa-flask:before { content: "🧪"; }
                        .fa-file-image:before { content: "🖼️"; }
                        .fa-spinner:before { content: "⟳"; }
                        .fa-exclamation-triangle:before { content: "⚠️"; }
                        .fa-check-circle:before { content: "✅"; }
                    `;
                    
                    this.injectCSS(css, 'fontawesome-fallback');
                    this.loadedResources.add('fontawesome');
                    pageLogger.debug('InlineResourceManager', 'loadFontAwesomeFallback', '✅ Font Awesome fallback已加载');
                },
                
                loadHtml2CanvasFallback() {
                    if (this.loadedResources.has('html2canvas')) return;
                    
                    // 创建简化的html2canvas替代
                    window.html2canvas = function(element, options = {}) {
                        return new Promise((resolve, reject) => {
                            pageLogger.warn('InlineResourceManager', 'html2canvas', '⚠️ 使用html2canvas fallback，功能受限');
                            
                            // 创建一个简单的canvas
                            const canvas = document.createElement('canvas');
                            const ctx = canvas.getContext('2d');
                            
                            // 设置canvas尺寸
                            const rect = element.getBoundingClientRect();
                            canvas.width = rect.width || 800;
                            canvas.height = rect.height || 600;
                            
                            // 填充白色背景
                            ctx.fillStyle = '#ffffff';
                            ctx.fillRect(0, 0, canvas.width, canvas.height);
                            
                            // 添加简单的文本提示
                            ctx.fillStyle = '#333333';
                            ctx.font = '16px Arial, sans-serif';
                            ctx.textAlign = 'center';
                            ctx.fillText('图片导出功能需要完整的html2canvas库', canvas.width / 2, canvas.height / 2);
                            ctx.fillText('请在HTTP服务器环境下使用完整功能', canvas.width / 2, canvas.height / 2 + 30);
                            
                            resolve(canvas);
                        });
                    };
                    
                    this.loadedResources.add('html2canvas');
                    pageLogger.debug('InlineResourceManager', 'loadHtml2CanvasFallback', '✅ html2canvas fallback已加载');
                },
                
                loadJsPDFFallback() {
                    if (this.loadedResources.has('jspdf')) return;
                    
                    // 创建简化的jsPDF替代
                    window.jspdf = {
                        jsPDF: function() {
                            return {
                                addImage: function() {
                                    pageLogger.warn('InlineResourceManager', 'jsPDF', '⚠️ 使用jsPDF fallback，功能受限');
                                },
                                save: function(filename) {
                                    pageLogger.warn('InlineResourceManager', 'jsPDF', '⚠️ PDF导出功能需要完整的jsPDF库');
                                    alert('PDF导出功能需要在HTTP服务器环境下使用。\n当前为简化模式，请使用浏览器的打印功能代替。');
                                }
                            };
                        }
                    };
                    
                    this.loadedResources.add('jspdf');
                    pageLogger.debug('InlineResourceManager', 'loadJsPDFFallback', '✅ jsPDF fallback已加载');
                },
                
                injectCSS(css, id) {
                    // 检查是否已存在
                    if (document.getElementById(id)) return;
                    
                    const style = document.createElement('style');
                    style.id = id;
                    style.textContent = css;
                    document.head.appendChild(style);
                }
            };
            
            // 设置全局引用
            window.inlineResourceManager = inlineResourceManager;
            
            // 加载所有资源
            await inlineResourceManager.loadAllResources();
            
            pageLogger.info('IndexPage', 'initializeInlineResourceManager', '✅ 内联资源管理器初始化完成');
        }
        
        /**
         * 创建增强的SmartOffice应用 - 统一内联架构
         * @function createEnhancedSmartOfficeApp
         * @param {Object} config - 应用配置
         * @returns {Promise<Object>} 增强的应用实例
         */
        async function createEnhancedSmartOfficeApp(config = {}) {
            pageLogger.info('IndexPage', 'createEnhancedSmartOfficeApp', '🔄 创建增强SmartOffice应用');
            
            // 创建增强的应用实例
            const enhancedApp = {
                config: config,
                isInitialized: false,
                managers: new Map(),
                stats: {
                    documentsCreated: 0,
                    exportsCompleted: 0,
                    nlpProcessed: 0,
                    startTime: Date.now()
                },
                
                // 应用信息
                getAppInfo() {
                    // 获取性能信息
                    const performanceInfo = this._getPerformanceInfo();
                    
                    return {
                        version: '2.0.0-enhanced',
                        name: 'SmartOffice (Enhanced Inline)',
                        mode: 'enhanced-inline',
                        managersCount: this.managers.size,
                        isInitialized: this.isInitialized,
                        timestamp: new Date().toISOString(),
                        features: {
                            nlp: 'gemini-only',
                            export: 'full',
                            offline: false,
                            modules: 'inline',
                            batch: true,
                            workflow: true,
                            plugins: true,
                            renderers: true,
                            templates: true,
                            exporters: true
                        },
                        stats: this.stats,
                        performance: performanceInfo
                    };
                },
                
                // 获取性能信息
                _getPerformanceInfo() {
                    const memoryInfo = performance.memory ? {
                        usedJSHeapSize: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + ' MB',
                        totalJSHeapSize: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + ' MB',
                        jsHeapSizeLimit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) + ' MB'
                    } : null;
                    
                    return {
                        uptime: Date.now() - this.stats.startTime,
                        memory: memoryInfo,
                        networkStatus: navigator.onLine ? 'online' : 'offline',
                        userAgent: navigator.userAgent,
                        language: navigator.language,
                        platform: navigator.platform
                    };
                },
                
                // 清理内存和资源
                cleanup() {
                    pageLogger.info('EnhancedApp', 'cleanup', '🧹 开始清理应用资源');
                    
                    // 清理管理器
                    for (const [name, manager] of this.managers) {
                        if (manager && typeof manager.cleanup === 'function') {
                            try {
                                manager.cleanup();
                                pageLogger.debug('EnhancedApp', 'cleanup', `管理器 ${name} 已清理`);
                            } catch (error) {
                                pageLogger.error('EnhancedApp', 'cleanup', `清理管理器 ${name} 失败`, { error: error.message });
                            }
                        }
                    }
                    
                    // 清理统计信息
                    this.stats = {
                        documentsCreated: 0,
                        exportsCompleted: 0,
                        nlpProcessed: 0,
                        startTime: Date.now()
                    };
                    
                    // 强制垃圾回收（如果可用）
                    if (window.gc) {
                        window.gc();
                    }
                    
                    pageLogger.info('EnhancedApp', 'cleanup', '✅ 应用资源清理完成');
                },
                
                // 初始化应用
                async init() {
                    if (this.isInitialized) {
                        return this;
                    }
                    
                    pageLogger.info('EnhancedApp', 'init', '🔄 开始初始化增强应用');
                    
                    try {
                        // 初始化核心管理器
                        pageLogger.debug('EnhancedApp', 'init', '初始化核心管理器');
                        
                        // 首先初始化统一渲染引擎
                        const renderEngine = this.createUnifiedRenderEngine();
                        pageLogger.info('EnhancedApp', 'init', '✅ 统一渲染引擎初始化完成');
                        
                        // 初始化其他核心管理器
                        this.createStateManager();
                        this.createEventManager();
                        this.createRenderersManager(); // 兼容性包装器
                        this.createTemplatesManager();
                        
                        pageLogger.info('EnhancedApp', 'init', '✅ 核心管理器初始化完成', {
                            managersCount: this.managers.size
                        });
                        
                        this.isInitialized = true;
                        pageLogger.info('EnhancedApp', 'init', '✅ 增强应用初始化完成');
                        
                        return this;
                        
                    } catch (error) {
                        pageLogger.error('EnhancedApp', 'init', '❌ 应用初始化失败', { error: error.message });
                        throw error;
                    }
                },
                
                // 获取管理器
                getManager(managerName) {
                    if (this.managers.has(managerName)) {
                        return this.managers.get(managerName);
                    }
                    
                    // 创建增强的管理器
                    switch (managerName) {
                        case 'nlp':
                            return this.createSmartNLPManager();
                        case 'offlineConfig':
                            return this.createEnhancedConfigManager();
                        case 'resourceManager':
                            return window.inlineResourceManager;
                        case 'state':
                            return this.createStateManager();
                        case 'events':
                            return this.createEventManager();
                        case 'workflow':
                            return this.createWorkflowManager();
                        case 'plugins':
                            return this.createPluginManager();
                        case 'renderers':
                            return this.createRenderersManager();
                        case 'renderEngine':
                            return this.createUnifiedRenderEngine();
                        case 'styleManager':
                            return this.getManager('renderEngine').styleManager;
                        case 'positionManager':
                            return this.getManager('renderEngine').positionManager;
                        case 'documentModel':
                            return this.getManager('renderEngine').documentModel;
                        case 'templates':
                            return this.createTemplatesManager();
                        case 'exporters':
                            return this.createExportersManager();
                        default:
                            pageLogger.warn('EnhancedApp', 'getManager', '未知管理器', { managerName });
                            return null;
                    }
                },
                
                // 内联Gemini NLP处理器
                createGeminiNLPProcessor(options = {}) {
                    /**
                     * @class GeminiNLPProcessor
                     * @description Gemini AI处理器 - 内联简化版本
                     */
                    class GeminiNLPProcessor {
                        constructor(options = {}) {
                            pageLogger.info('GeminiNLPProcessor', 'constructor', '🤖 初始化Gemini处理器');
                            
                            // API配置
                            this.apiKey = options.apiKey;
                            this.apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent';
                            
                            // 处理配置
                            this.config = {
                                maxRetries: 3,
                                timeout: 30000,
                                temperature: 0.1,
                                topP: 0.8,
                                topK: 40,
                                maxOutputTokens: 2048,
                                ...options
                            };
                            
                            // 性能统计
                            this.stats = {
                                processedTexts: 0,
                                processedImages: 0,
                                apiCalls: 0,
                                errors: 0,
                                processingTime: 0
                            };
                            
                            pageLogger.info('GeminiNLPProcessor', 'constructor', '✅ Gemini处理器初始化完成');
                        }
                        
                        async processText(text, documentType = 'receipt') {
                            const startTime = performance.now();
                            this.stats.processedTexts++;
                            this.stats.apiCalls++;
                            
                            pageLogger.info('GeminiNLPProcessor', 'processText', '🔍 开始处理文本', {
                                textLength: text.length,
                                documentType
                            });
                            
                            if (!this.apiKey) {
                                this.stats.errors++;
                                const error = new Error('Gemini API密钥未配置');
                                pageLogger.error('GeminiNLPProcessor', 'processText', '❌ API密钥未配置', { error: error.message });
                                throw error;
                            }
                            
                            try {
                                const prompt = this._buildPrompt(text, documentType);
                                const requestBody = {
                                    contents: [{
                                        parts: [{ text: prompt }]
                                    }],
                                    generationConfig: {
                                        temperature: this.config.temperature,
                                        topP: this.config.topP,
                                        topK: this.config.topK,
                                        maxOutputTokens: this.config.maxOutputTokens
                                    }
                                };
                                
                                const response = await this._callGeminiAPI(requestBody);
                                const result = this._parseGeminiResponse(response, documentType);
                                
                                const processingTime = performance.now() - startTime;
                                this.stats.processingTime += processingTime;
                                
                                pageLogger.info('GeminiNLPProcessor', 'processText', '✅ 文本处理完成', {
                                    confidence: result.confidence,
                                    processingTime: `${processingTime.toFixed(2)}ms`
                                });
                                
                                return {
                                    success: true,
                                    confidence: result.confidence || 0.8,
                                    extractedData: result.extractedData || {},
                                    processingTime,
                                    timestamp: new Date().toISOString()
                                };
                                
                            } catch (error) {
                                this.stats.errors++;
                                pageLogger.error('GeminiNLPProcessor', 'processText', '❌ 文本处理失败', { error: error.message });
                                throw error;
                            }
                        }
                        
                        async processImage(image, text = '', documentType = 'receipt') {
                            const startTime = performance.now();
                            this.stats.processedImages++;
                            this.stats.apiCalls++;
                            
                            pageLogger.info('GeminiNLPProcessor', 'processImage', '🖼️ 开始处理图片');
                            
                            if (!this.apiKey) {
                                throw new Error('Gemini API密钥未配置');
                            }
                            
                            try {
                                const imageData = await this._prepareImageData(image);
                                const prompt = this._buildImagePrompt(documentType);
                                
                                const requestBody = {
                                    contents: [{
                                        parts: [
                                            { text: prompt },
                                            {
                                                inline_data: {
                                                    mime_type: imageData.mimeType,
                                                    data: imageData.data
                                                }
                                            }
                                        ]
                                    }],
                                    generationConfig: {
                                        temperature: this.config.temperature,
                                        topP: this.config.topP,
                                        topK: this.config.topK,
                                        maxOutputTokens: this.config.maxOutputTokens
                                    }
                                };
                                
                                const response = await this._callGeminiAPI(requestBody);
                                const result = this._parseGeminiResponse(response, documentType);
                                
                                const processingTime = performance.now() - startTime;
                                this.stats.processingTime += processingTime;
                                
                                pageLogger.info('GeminiNLPProcessor', 'processImage', '✅ 图片处理完成');
                                
                                return {
                                    success: true,
                                    confidence: result.confidence || 0.8,
                                    extractedData: result.extractedData || {},
                                    processingTime,
                                    timestamp: new Date().toISOString()
                                };
                                
                            } catch (error) {
                                this.stats.errors++;
                                pageLogger.error('GeminiNLPProcessor', 'processImage', '❌ 图片处理失败', { error: error.message });
                                throw error;
                            }
                        }
                        
                        async processMultiModal(text, image, documentType = 'receipt') {
                            // 简化版本：优先使用图片处理
                            if (image) {
                                return await this.processImage(image, text, documentType);
                            } else {
                                return await this.processText(text, documentType);
                            }
                        }
                        
                        _buildPrompt(text, documentType) {
                            const basePrompt = `你是一个专业的文档信息提取助手。请从提供的内容中提取结构化信息。

请严格按照以下JSON格式返回结果，不要添加任何其他文字：

{
  "documentType": "${documentType}",
  "extractedData": {
    "customerName": "客户姓名",
    "totalAmount": "总金额",
    "currency": "货币类型(RM/RMB)",
    "date": "日期",
    "services": ["服务项目"],
    "phone": "电话号码",
    "paymentMethod": "支付方式"
  },
  "confidence": 0.95
}

请分析以下内容：
${text}`;
                            
                            return basePrompt;
                        }
                        
                        _buildImagePrompt(documentType) {
                            return `请分析这张图片中的文档信息，提取结构化数据。返回JSON格式，包含文档类型为"${documentType}"的相关字段。`;
                        }
                        
                        async _prepareImageData(image) {
                            let base64Data;
                            let mimeType = 'image/png';
                            
                            if (typeof image === 'string') {
                                if (image.startsWith('data:')) {
                                    const [header, data] = image.split(',');
                                    mimeType = header.match(/data:([^;]+)/)[1];
                                    base64Data = data;
                                } else {
                                    base64Data = image;
                                }
                            } else if (image instanceof File) {
                                base64Data = await this._fileToBase64(image);
                                mimeType = image.type;
                            }
                            
                            return { data: base64Data, mimeType };
                        }
                        
                        async _fileToBase64(file) {
                            return new Promise((resolve, reject) => {
                                const reader = new FileReader();
                                reader.onload = () => {
                                    const result = reader.result.split(',')[1];
                                    resolve(result);
                                };
                                reader.onerror = reject;
                                reader.readAsDataURL(file);
                            });
                        }
                        
                        async _callGeminiAPI(requestBody) {
                            // 检查网络连接
                            if (!navigator.onLine) {
                                throw new Error('网络连接不可用，请检查网络设置');
                            }
                            
                            const url = `${this.apiUrl}?key=${this.apiKey}`;
                            const controller = new AbortController();
                            const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时
                            
                            try {
                                const response = await fetch(url, {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json'
                                    },
                                    body: JSON.stringify(requestBody),
                                    signal: controller.signal
                                });
                                
                                clearTimeout(timeoutId);
                                
                                if (!response.ok) {
                                    const errorData = await response.json().catch(() => ({}));
                                    
                                    // 根据错误状态码提供更详细的错误信息
                                    let errorMessage = `Gemini API错误: ${response.status}`;
                                    
                                    switch (response.status) {
                                        case 400:
                                            errorMessage += ' - 请求格式错误';
                                            break;
                                        case 401:
                                            errorMessage += ' - API密钥无效';
                                            break;
                                        case 403:
                                            errorMessage += ' - 访问被拒绝，请检查API密钥权限';
                                            break;
                                        case 429:
                                            errorMessage += ' - 请求频率过高，请稍后重试';
                                            break;
                                        case 500:
                                            errorMessage += ' - 服务器内部错误';
                                            break;
                                        default:
                                            errorMessage += ` - ${errorData.error?.message || response.statusText}`;
                                    }
                                    
                                    throw new Error(errorMessage);
                                }
                                
                                return await response.json();
                                
                            } catch (error) {
                                clearTimeout(timeoutId);
                                
                                if (error.name === 'AbortError') {
                                    throw new Error('请求超时，请检查网络连接或稍后重试');
                                }
                                
                                // 网络错误处理
                                if (error instanceof TypeError && error.message.includes('fetch')) {
                                    throw new Error('网络连接失败，请检查网络设置');
                                }
                                
                                throw error;
                            }
                        }
                        
                        _parseGeminiResponse(response, documentType) {
                            try {
                                const content = response.candidates?.[0]?.content?.parts?.[0]?.text;
                                if (!content) {
                                    throw new Error('Gemini响应格式错误');
                                }
                                
                                // 尝试解析JSON
                                const jsonMatch = content.match(/\{[\s\S]*\}/);
                                if (!jsonMatch) {
                                    throw new Error('无法从响应中提取JSON');
                                }
                                
                                const result = JSON.parse(jsonMatch[0]);
                                return result;
                                
                            } catch (error) {
                                pageLogger.warn('GeminiNLPProcessor', '_parseGeminiResponse', '解析响应失败，使用默认结果', { error: error.message });
                                return {
                                    documentType,
                                    extractedData: {},
                                    confidence: 0.1
                                };
                            }
                        }
                        
                        setApiKey(apiKey) {
                            this.apiKey = apiKey;
                            pageLogger.info('GeminiNLPProcessor', 'setApiKey', '🔑 API密钥已更新');
                        }
                        
                        getStats() {
                            return {
                                ...this.stats,
                                averageProcessingTime: this.stats.apiCalls > 0 
                                    ? this.stats.processingTime / this.stats.apiCalls 
                                    : 0,
                                successRate: this.stats.apiCalls > 0
                                    ? ((this.stats.apiCalls - this.stats.errors) / this.stats.apiCalls * 100).toFixed(2) + '%'
                                    : '0%'
                            };
                        }
                        
                        reset() {
                            this.stats = {
                                processedTexts: 0,
                                processedImages: 0,
                                apiCalls: 0,
                                errors: 0,
                                processingTime: 0
                            };
                        }
                    }
                    
                    return new GeminiNLPProcessor(options);
                },

                // 获取Gemini NLP处理器实例（单例模式）
                getGeminiNLPProcessor(options = {}) {
                    if (!this._geminiNLPInstance) {
                        this._geminiNLPInstance = this.createGeminiNLPProcessor(options);
                    } else if (options.apiKey) {
                        this._geminiNLPInstance.setApiKey(options.apiKey);
                    }
                    return this._geminiNLPInstance;
                },

                // 创建Gemini专用NLP管理器
                createSmartNLPManager() {
                    if (this.managers.has('nlp')) {
                        return this.managers.get('nlp');
                    }
                    
                    const smartNLPManager = {
                        geminiProcessor: null, // 将在设置API密钥时初始化
                        stats: {
                            totalRequests: 0,
                            geminiSuccess: 0,
                            geminiFailures: 0,
                            averageResponseTime: 0
                        },
                        
                        async processText(text, documentType = 'receipt') {
                            const startTime = performance.now();
                            this.stats.totalRequests++;
                            
                            pageLogger.info('SmartNLP', 'processText', '🧠 Gemini文本处理', {
                                textLength: text.length,
                                documentType,
                                requestId: this.stats.totalRequests
                            });
                            
                            // 检查Gemini处理器是否可用
                            if (!this.geminiProcessor) {
                                const error = new Error('Gemini处理器未初始化，请先设置API密钥');
                                pageLogger.error('SmartNLP', 'processText', '❌ Gemini处理器不可用', { error: error.message });
                                this.stats.geminiFailures++;
                                throw error;
                            }
                            
                            try {
                                const result = await this.geminiProcessor.processText(text, documentType);
                                    this.stats.geminiSuccess++;
                            
                            // 更新统计信息
                            const responseTime = performance.now() - startTime;
                            this.stats.averageResponseTime = (this.stats.averageResponseTime * (this.stats.totalRequests - 1) + responseTime) / this.stats.totalRequests;
                            
                            // 增强结果信息
                                result.processorUsed = 'gemini';
                            result.responseTime = responseTime;
                            result.requestId = this.stats.totalRequests;
                            result.timestamp = new Date().toISOString();
                            
                            // 更新应用统计
                            enhancedApp.stats.nlpProcessed++;
                                
                                pageLogger.info('SmartNLP', 'processText', '✅ Gemini处理成功', {
                                    confidence: result.confidence,
                                    responseTime: `${responseTime.toFixed(2)}ms`
                                });
                            
                            return result;
                                
                            } catch (error) {
                                this.stats.geminiFailures++;
                                pageLogger.error('SmartNLP', 'processText', '❌ Gemini处理失败', { error: error.message });
                                throw error;
                            }
                        },
                        
                        async processImage(image, text = '', documentType = 'receipt') {
                            pageLogger.info('SmartNLP', 'processImage', '🖼️ Gemini图片处理');
                            
                            // 检查Gemini处理器是否可用
                            if (!this.geminiProcessor) {
                                const error = new Error('Gemini处理器未初始化，请先设置API密钥');
                                pageLogger.error('SmartNLP', 'processImage', '❌ Gemini处理器不可用', { error: error.message });
                                throw error;
                            }
                            
                            try {
                                const result = await this.geminiProcessor.processImage(image, text, documentType);
                                result.imageProcessed = true;
                                result.processorUsed = 'gemini';
                                
                                pageLogger.info('SmartNLP', 'processImage', '✅ Gemini图片处理成功');
                                return result;
                                
                            } catch (error) {
                                pageLogger.error('SmartNLP', 'processImage', '❌ Gemini图片处理失败', { error: error.message });
                                throw error;
                            }
                        },
                        
                        async processMultiModal(text, image, documentType = 'receipt') {
                            pageLogger.info('SmartNLP', 'processMultiModal', '🔄 Gemini多模态处理');
                            
                            // 检查Gemini处理器是否可用
                            if (!this.geminiProcessor) {
                                const error = new Error('Gemini处理器未初始化，请先设置API密钥');
                                pageLogger.error('SmartNLP', 'processMultiModal', '❌ Gemini处理器不可用', { error: error.message });
                                throw error;
                            }
                            
                            // 多模态处理使用Gemini
                            if (this.geminiProcessor.processMultiModal) {
                                try {
                                    const result = await this.geminiProcessor.processMultiModal(text, image, documentType);
                                    result.multiModalProcessed = true;
                                    result.processorUsed = 'gemini';
                                    
                                    pageLogger.info('SmartNLP', 'processMultiModal', '✅ Gemini多模态处理成功');
                                    return result;
                                    
                                } catch (error) {
                                    pageLogger.error('SmartNLP', 'processMultiModal', '❌ Gemini多模态处理失败', { error: error.message });
                                    throw error;
                                }
                            } else {
                            // 降级到图片处理
                                pageLogger.info('SmartNLP', 'processMultiModal', '🔄 降级到图片处理');
                            return await this.processImage(image, text, documentType);
                            }
                        },
                        
                        setApiKey(apiKey) {
                            if (apiKey && apiKey.trim()) {
                                // 初始化Gemini处理器
                                try {
                                    this.geminiProcessor = getGeminiNLPProcessor({ apiKey });
                                    pageLogger.info('SmartNLP', 'setApiKey', '✅ Gemini处理器已启用');
                                } catch (error) {
                                    pageLogger.error('SmartNLP', 'setApiKey', '❌ Gemini处理器初始化失败', { error: error.message });
                                    throw error;
                                }
                            } else {
                                this.geminiProcessor = null;
                                pageLogger.info('SmartNLP', 'setApiKey', '🔄 已禁用Gemini处理器');
                            }
                        },
                        
                        isReady() {
                            return !!this.geminiProcessor;
                        },
                        
                        getStats() {
                            const geminiSuccessRate = this.stats.totalRequests > 0 ? 
                                (this.stats.geminiSuccess / this.stats.totalRequests * 100).toFixed(1) : '0.0';

                            return {
                                ...this.stats,
                                geminiSuccessRate: `${geminiSuccessRate}%`,
                                geminiAvailable: !!this.geminiProcessor,
                                mode: 'gemini-only'
                            };
                        },
                        
                        reset() {
                            this.stats = {
                                totalRequests: 0,
                                geminiSuccess: 0,
                                geminiFailures: 0,
                                averageResponseTime: 0
                            };
                        }
                    };
                    
                    this.managers.set('nlp', smartNLPManager);
                    return smartNLPManager;
                },
                
                // 创建增强的配置管理器
                createEnhancedConfigManager() {
                    if (this.managers.has('offlineConfig')) {
                        return this.managers.get('offlineConfig');
                    }
                    
                    const configManager = {
                        config: {
                            mode: 'enhanced',
                            app: {
                                language: 'zh-CN',
                                theme: 'default',
                                autoSave: true,
                                enableDebug: false
                            },
                            nlp: {
                                enabled: true,
                                mode: 'smart',
                                geminiEnabled: false,
                                confidence: {
                                    minimum: 0.3,
                                    good: 0.6,
                                    excellent: 0.8
                                }
                            },
                            export: {
                                defaultFormat: 'pdf',
                                autoDownload: true,
                                showProgress: true,
                                enableBatch: true
                            },
                            ui: {
                                showToolbar: true,
                                showSidebar: true,
                                showStatusBar: true,
                                enableKeyboardShortcuts: true
                            }
                        },
                        listeners: new Map(),
                        
                        get(path, defaultValue = undefined) {
                            const keys = path.split('.');
                            let value = this.config;
                            
                            for (const key of keys) {
                                if (value && typeof value === 'object' && key in value) {
                                    value = value[key];
                                } else {
                                    return defaultValue;
                                }
                            }
                            
                            return value;
                        },
                        
                        set(path, value) {
                            const keys = path.split('.');
                            let current = this.config;
                            
                            for (let i = 0; i < keys.length - 1; i++) {
                                const key = keys[i];
                                if (!(key in current) || typeof current[key] !== 'object') {
                                    current[key] = {};
                                }
                                current = current[key];
                            }
                            
                            const oldValue = current[keys[keys.length - 1]];
                            current[keys[keys.length - 1]] = value;
                            
                            // 触发变更事件
                            this.notifyListeners(path, value, oldValue);
                        },
                        
                        on(event, listener) {
                            if (!this.listeners.has(event)) {
                                this.listeners.set(event, []);
                            }
                            this.listeners.get(event).push(listener);
                        },
                        
                        notifyListeners(path, newValue, oldValue) {
                            const listeners = this.listeners.get('change') || [];
                            listeners.forEach(listener => {
                                try {
                                    listener({ path, newValue, oldValue });
                                } catch (error) {
                                    pageLogger.error('ConfigManager', 'notifyListeners', '监听器执行失败', { error: error.message });
                                }
                            });
                        },
                        
                        isOfflineMode() {
                            return true;
                        },
                        
                        export() {
                            return JSON.parse(JSON.stringify(this.config));
                        },
                        
                        import(configData) {
                            this.config = { ...this.config, ...configData };
                        }
                    };
                    
                    this.managers.set('offlineConfig', configManager);
                    return configManager;
                },
                
                // 创建状态管理器
                createStateManager() {
                    if (this.managers.has('state')) {
                        return this.managers.get('state');
                    }
                    
                    const stateManager = {
                        state: {
                            currentDocument: null,
                            documentType: 'receipt',
                            isProcessing: false,
                            lastError: null,
                            history: []
                        },
                        listeners: new Map(),
                        
                        get(key) {
                            return this.state[key];
                        },
                        
                        set(key, value) {
                            const oldValue = this.state[key];
                            this.state[key] = value;
                            this.notifyListeners(key, value, oldValue);
                        },
                        
                        on(event, listener) {
                            if (!this.listeners.has(event)) {
                                this.listeners.set(event, []);
                            }
                            this.listeners.get(event).push(listener);
                        },
                        
                        notifyListeners(key, newValue, oldValue) {
                            const listeners = this.listeners.get('change') || [];
                            listeners.forEach(listener => {
                                try {
                                    listener({ key, newValue, oldValue });
                                } catch (error) {
                                    pageLogger.error('StateManager', 'notifyListeners', '监听器执行失败', { error: error.message });
                                }
                            });
                        },
                        
                        getState() {
                            return { ...this.state };
                        }
                    };
                    
                    this.managers.set('state', stateManager);
                    return stateManager;
                },
                
                // 创建事件管理器
                createEventManager() {
                    if (this.managers.has('events')) {
                        return this.managers.get('events');
                    }
                    
                    const eventManager = {
                        listeners: new Map(),
                        
                        on(event, listener) {
                            if (!this.listeners.has(event)) {
                                this.listeners.set(event, []);
                            }
                            this.listeners.get(event).push(listener);
                        },
                        
                        off(event, listener) {
                            if (this.listeners.has(event)) {
                                const listeners = this.listeners.get(event);
                                const index = listeners.indexOf(listener);
                                if (index > -1) {
                                    listeners.splice(index, 1);
                                }
                            }
                        },
                        
                        emit(event, data) {
                            const listeners = this.listeners.get(event) || [];
                            listeners.forEach(listener => {
                                try {
                                    listener(data);
                                } catch (error) {
                                    pageLogger.error('EventManager', 'emit', '事件监听器执行失败', { event, error: error.message });
                                }
                            });
                        },
                        
                        getListenerCount(event) {
                            return this.listeners.has(event) ? this.listeners.get(event).length : 0;
                        }
                    };
                    
                    this.managers.set('events', eventManager);
                    return eventManager;
                },
                
                // 创建工作流管理器
                createWorkflowManager() {
                    if (this.managers.has('workflow')) {
                        return this.managers.get('workflow');
                    }
                    
                    const workflowManager = {
                        workflows: new Map(),
                        
                        register(name, workflow) {
                            this.workflows.set(name, workflow);
                            pageLogger.info('WorkflowManager', 'register', '工作流已注册', { name });
                        },
                        
                        async execute(name, data) {
                            if (!this.workflows.has(name)) {
                                throw new Error(`工作流 ${name} 未找到`);
                            }
                            
                            const workflow = this.workflows.get(name);
                            pageLogger.info('WorkflowManager', 'execute', '执行工作流', { name });
                            
                            try {
                                const result = await workflow(data);
                                pageLogger.info('WorkflowManager', 'execute', '工作流执行成功', { name });
                                return result;
                            } catch (error) {
                                pageLogger.error('WorkflowManager', 'execute', '工作流执行失败', { name, error: error.message });
                                throw error;
                            }
                        },
                        
                        list() {
                            return Array.from(this.workflows.keys());
                        }
                    };
                    
                    this.managers.set('workflow', workflowManager);
                    return workflowManager;
                },
                
                // 创建插件管理器
                createPluginManager() {
                    if (this.managers.has('plugins')) {
                        return this.managers.get('plugins');
                    }
                    
                    const pluginManager = {
                        plugins: new Map(),
                        
                        register(name, plugin) {
                            this.plugins.set(name, plugin);
                            pageLogger.info('PluginManager', 'register', '插件已注册', { name });
                        },
                        
                        get(name) {
                            return this.plugins.get(name);
                        },
                        
                        list() {
                            return Array.from(this.plugins.keys());
                        },
                        
                        async initialize() {
                            for (const [name, plugin] of this.plugins) {
                                if (plugin.initialize) {
                                    try {
                                        await plugin.initialize();
                                        pageLogger.info('PluginManager', 'initialize', '插件初始化成功', { name });
                                    } catch (error) {
                                        pageLogger.error('PluginManager', 'initialize', '插件初始化失败', { name, error: error.message });
                                    }
                                }
                            }
                        }
                    };
                    
                    this.managers.set('plugins', pluginManager);
                    return pluginManager;
                },
                
                // 创建统一渲染引擎
                createUnifiedRenderEngine() {
                    if (this.managers.has('renderEngine')) {
                        return this.managers.get('renderEngine');
                    }
                    
                    // 创建文档模型
                    const documentModel = {
                        data: {},
                        template: null,
                        metadata: {},
                        
                        setData(data) {
                            this.data = { ...data };
                        },
                        
                        setTemplate(template) {
                            this.template = template;
                        },
                        
                        setMetadata(metadata) {
                            this.metadata = { ...metadata };
                        },
                        
                        getModel() {
                            return {
                                data: this.data,
                                template: this.template,
                                metadata: this.metadata
                            };
                        }
                    };
                    
                    // 创建样式管理器
                    const styleManager = {
                        styles: new Map(),
                        themes: new Map(),
                        currentTheme: 'default',
                        
                        addStyle(selector, styles) {
                            this.styles.set(selector, styles);
                        },
                        
                        getStyle(selector) {
                            return this.styles.get(selector) || {};
                        },
                        
                        setTheme(themeName) {
                            this.currentTheme = themeName;
                        },
                        
                        generateCSS() {
                            let css = '';
                            for (const [selector, styles] of this.styles) {
                                css += `${selector} { ${Object.entries(styles).map(([prop, value]) => `${prop}: ${value}`).join('; ')} }\n`;
                            }
                            return css;
                        }
                    };
                    
                    // 创建位置管理器
                    const positionManager = {
                        elements: new Map(),
                        layout: 'default',
                        
                        setPosition(elementId, position) {
                            this.elements.set(elementId, position);
                        },
                        
                        getPosition(elementId) {
                            return this.elements.get(elementId) || { x: 0, y: 0, width: 'auto', height: 'auto' };
                        },
                        
                        setLayout(layoutType) {
                            this.layout = layoutType;
                        },
                        
                        calculateLayout() {
                            // 布局计算逻辑
                            return Array.from(this.elements.entries());
                        }
                    };
                    
                    // 创建统一渲染引擎
                    const renderEngine = {
                        documentModel,
                        styleManager,
                        positionManager,
                        renderers: new Map(),
                        defaultRenderer: 'html',
                        
                        // 注册渲染器
                        registerRenderer(name, renderer) {
                            this.renderers.set(name, renderer);
                            pageLogger.info('UnifiedRenderEngine', 'registerRenderer', '渲染器已注册', { name });
                        },
                        
                        // 获取渲染器
                        getRenderer(name) {
                            return this.renderers.get(name) || this.renderers.get(this.defaultRenderer);
                        },
                        
                        // 统一渲染接口
                        async render(template, data, options = {}) {
                            const rendererName = options.renderer || this.defaultRenderer;
                            const renderer = this.getRenderer(rendererName);
                            
                            if (!renderer) {
                                throw new Error(`渲染器未找到: ${rendererName}`);
                            }
                            
                            // 设置文档模型
                            this.documentModel.setTemplate(template);
                            this.documentModel.setData(data);
                            this.documentModel.setMetadata(options.metadata || {});
                            
                            // 应用样式和布局
                            if (options.styles) {
                                Object.entries(options.styles).forEach(([selector, styles]) => {
                                    this.styleManager.addStyle(selector, styles);
                                });
                            }
                            
                            if (options.positions) {
                                Object.entries(options.positions).forEach(([elementId, position]) => {
                                    this.positionManager.setPosition(elementId, position);
                                });
                            }
                            
                            // 执行渲染
                            return await renderer.render(this.documentModel.getModel(), {
                                ...options,
                                styles: this.styleManager.generateCSS(),
                                layout: this.positionManager.calculateLayout()
                            });
                        },
                        
                        // 切换模板
                        switchTemplate(templateType) {
                            pageLogger.info('UnifiedRenderEngine', 'switchTemplate', '切换模板', { templateType });
                            return true;
                        },
                        
                        // 获取统计信息
                        getStats() {
                            return {
                                totalRenderers: this.renderers.size,
                                defaultRenderer: this.defaultRenderer,
                                availableRenderers: Array.from(this.renderers.keys()),
                                stylesCount: this.styleManager.styles.size,
                                elementsCount: this.positionManager.elements.size,
                                currentTheme: this.styleManager.currentTheme,
                                currentLayout: this.positionManager.layout
                            };
                        }
                    };
                    
                    // 注册默认HTML渲染器
                    renderEngine.registerRenderer('html', {
                        async render(documentModel, options = {}) {
                            const { data, template, metadata } = documentModel;
                            // 增强的HTML渲染逻辑
                            let html = `<div class="document" data-template="${template || 'default'}">`;
                            
                            // 应用样式
                            if (options.styles) {
                                html += `<style>${options.styles}</style>`;
                            }
                            
                            // 渲染内容
                            html += `<div class="content">${JSON.stringify(data, null, 2)}</div>`;
                            
                            // 添加元数据
                            if (metadata && Object.keys(metadata).length > 0) {
                                html += `<div class="metadata">${JSON.stringify(metadata)}</div>`;
                            }
                            
                            html += '</div>';
                            return html;
                        }
                    });
                    
                    this.managers.set('renderEngine', renderEngine);
                    this.managers.set('styleManager', styleManager);
                    this.managers.set('positionManager', positionManager);
                    this.managers.set('documentModel', documentModel);
                    
                    return renderEngine;
                },
                
                // 创建渲染器管理器（向后兼容）
                createRenderersManager() {
                    if (this.managers.has('renderers')) {
                        return this.managers.get('renderers');
                    }
                    
                    // 获取统一渲染引擎
                    const renderEngine = this.createUnifiedRenderEngine();
                    
                    // 创建兼容性包装器
                    const renderersManager = {
                        renderers: renderEngine.renderers,
                        defaultRenderer: renderEngine.defaultRenderer,
                        
                        // 注册渲染器（兼容旧接口）
                        register(name, renderer) {
                            return renderEngine.registerRenderer(name, renderer);
                        },
                        
                        // 获取渲染器（兼容旧接口）
                        get(name) {
                            return renderEngine.getRenderer(name);
                        },
                        
                        // 渲染文档（兼容旧接口）
                        async render(template, data, options = {}) {
                            return await renderEngine.render(template, data, options);
                        },
                        
                        // 切换模板（兼容旧接口）
                        switchTemplate(templateType) {
                            return renderEngine.switchTemplate(templateType);
                        },
                        
                        // 获取统计信息（兼容旧接口）
                        getStats() {
                            return renderEngine.getStats();
                        }
                    };
                    
                    this.managers.set('renderers', renderersManager);
                    return renderersManager;
                },
                
                // 创建模板管理器
                createTemplatesManager() {
                    if (this.managers.has('templates')) {
                        return this.managers.get('templates');
                    }
                    
                    const templatesManager = {
                        templates: new Map(),
                        defaultTemplate: 'receipt',
                        
                        // 注册模板
                        register(name, template) {
                            this.templates.set(name, template);
                            pageLogger.info('TemplatesManager', 'register', '模板已注册', { name });
                        },
                        
                        // 获取模板
                        get(name) {
                            return this.templates.get(name) || this.templates.get(this.defaultTemplate);
                        },
                        
                        // 获取所有模板
                        getAll() {
                            return Array.from(this.templates.entries());
                        },
                        
                        // 获取统计信息
                        getStats() {
                            return {
                                totalTemplates: this.templates.size,
                                defaultTemplate: this.defaultTemplate,
                                availableTemplates: Array.from(this.templates.keys())
                            };
                        }
                    };
                    
                    // 注册默认模板
                    const defaultTemplates = ['receipt', 'invoice', 'quotation', 'driver_agreement'];
                    defaultTemplates.forEach(type => {
                        templatesManager.register(type, {
                            name: type,
                            type: type,
                            fields: [],
                            render: (data) => `<div class="${type}">${JSON.stringify(data)}</div>`
                        });
                    });
                    
                    this.managers.set('templates', templatesManager);
                    return templatesManager;
                },
                
                // 创建导出器管理器
                createExportersManager() {
                    if (this.managers.has('exporters')) {
                        return this.managers.get('exporters');
                    }
                    
                    const exportersManager = {
                        exporters: new Map(),
                        defaultExporter: 'pdf',
                        
                        // 注册导出器
                        register(name, exporter) {
                            this.exporters.set(name, exporter);
                            pageLogger.info('ExportersManager', 'register', '导出器已注册', { name });
                        },
                        
                        // 获取导出器
                        get(name) {
                            return this.exporters.get(name) || this.exporters.get(this.defaultExporter);
                        },
                        
                        // 导出文档
                        async export(content, format, options = {}) {
                            const exporterName = format || this.defaultExporter;
                            const exporter = this.get(exporterName);
                            
                            if (!exporter) {
                                throw new Error(`导出器未找到: ${exporterName}`);
                            }
                            
                            return await exporter.export(content, options);
                        },
                        
                        // 获取统计信息
                        getStats() {
                            return {
                                totalExporters: this.exporters.size,
                                defaultExporter: this.defaultExporter,
                                availableExporters: Array.from(this.exporters.keys())
                            };
                        }
                    };
                    
                    // 注册默认导出器
                    exportersManager.register('pdf', {
                        async export(content, options = {}) {
                            pageLogger.info('PDFExporter', 'export', '导出PDF');
                            // 简化的PDF导出逻辑
                            return { success: true, format: 'pdf', content };
                        }
                    });
                    
                    exportersManager.register('image', {
                        async export(content, options = {}) {
                            pageLogger.info('ImageExporter', 'export', '导出图片');
                            // 简化的图片导出逻辑
                            return { success: true, format: 'image', content };
                        }
                    });
                    
                    this.managers.set('exporters', exportersManager);
                    return exportersManager;
                },
                
                // 批量处理功能
                async batchProcess(documents, options = {}) {
                    pageLogger.info('EnhancedApp', 'batchProcess', '🔄 开始批量处理', { count: documents.length });
                    
                    const results = [];
                    const errors = [];
                    const maxRetries = options.maxRetries || 2;
                    const retryDelay = options.retryDelay || 1000;
                    
                    for (let i = 0; i < documents.length; i++) {
                        let retryCount = 0;
                        let success = false;
                        
                        while (!success && retryCount <= maxRetries) {
                        try {
                            const result = await this.processDocument(documents[i], options);
                            results.push(result);
                                success = true;
                            
                            // 触发进度事件
                            this.getManager('events').emit('batch:progress', {
                                current: i + 1,
                                total: documents.length,
                                progress: ((i + 1) / documents.length * 100).toFixed(1)
                            });
                                
                                if (retryCount > 0) {
                                    pageLogger.info('EnhancedApp', 'batchProcess', '✅ 重试成功', { 
                                        index: i, 
                                        retryCount 
                                    });
                                }
                                
                        } catch (error) {
                                retryCount++;
                                
                                if (retryCount <= maxRetries) {
                                    pageLogger.warn('EnhancedApp', 'batchProcess', '⚠️ 处理失败，准备重试', { 
                                        index: i, 
                                        retryCount, 
                                        maxRetries,
                                        error: error.message 
                                    });
                                    
                                    // 等待重试延迟
                                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                                } else {
                                    errors.push({ 
                                        index: i, 
                                        error: error.message,
                                        retryCount: retryCount - 1
                                    });
                                    pageLogger.error('EnhancedApp', 'batchProcess', '❌ 文档处理最终失败', { 
                                        index: i, 
                                        error: error.message,
                                        totalRetries: retryCount - 1
                                    });
                                }
                            }
                        }
                    }
                    
                    pageLogger.info('EnhancedApp', 'batchProcess', '✅ 批量处理完成', { 
                        success: results.length, 
                        errors: errors.length,
                        totalRetries: errors.reduce((sum, err) => sum + (err.retryCount || 0), 0)
                    });
                    
                    return { results, errors };
                },
                
                // 处理单个文档
                async processDocument(documentData, options = {}) {
                    const nlpManager = this.getManager('nlp');
                    const stateManager = this.getManager('state');
                    
                    stateManager.set('isProcessing', true);
                    
                    try {
                        let result;
                        
                        if (documentData.text && documentData.image) {
                            result = await nlpManager.processMultiModal(documentData.text, documentData.image, documentData.type);
                        } else if (documentData.image) {
                            result = await nlpManager.processImage(documentData.image, documentData.text || '', documentData.type);
                        } else {
                            result = await nlpManager.processText(documentData.text, documentData.type);
                        }
                        
                        this.stats.documentsCreated++;
                        stateManager.set('currentDocument', result);
                        
                        return result;
                    } finally {
                        stateManager.set('isProcessing', false);
                    }
                },
                
                // 初始化应用
                async initialize() {
                    pageLogger.info('EnhancedApp', 'initialize', '🔄 初始化增强应用');
                    
                    // 创建所有管理器
                    this.createSmartNLPManager();
                    this.createEnhancedConfigManager();
                    this.createStateManager();
                    this.createEventManager();
                    this.createWorkflowManager();
                    this.createPluginManager();
                    this.createRenderersManager();
                    this.createTemplatesManager();
                    this.createExportersManager();
                    
                    // 初始化插件
                    await this.getManager('plugins').initialize();
                    
                    // 注册默认工作流
                    this.registerDefaultWorkflows();
                    
                    this.isInitialized = true;
                    pageLogger.info('EnhancedApp', 'initialize', '✅ 增强应用初始化完成');
                    
                    return this;
                },
                
                // 注册默认工作流
                registerDefaultWorkflows() {
                    const workflowManager = this.getManager('workflow');
                    
                    // 文档生成工作流
                    workflowManager.register('document-generation', async (data) => {
                        const result = await this.processDocument(data);
                        this.getManager('events').emit('document:generated', result);
                        return result;
                    });
                    
                    // 批量导出工作流
                    workflowManager.register('batch-export', async (data) => {
                        const results = await this.batchProcess(data.documents, data.options);
                        this.getManager('events').emit('batch:completed', results);
                        return results;
                    });
                },
                
                // 销毁应用
                async destroy() {
                    pageLogger.info('EnhancedApp', 'destroy', '🔄 销毁增强应用');
                    this.managers.clear();
                    this.isInitialized = false;
                    pageLogger.info('EnhancedApp', 'destroy', '✅ 增强应用已销毁');
                }
            };
            
            // 初始化应用
            await enhancedApp.initialize();
            
            pageLogger.info('IndexPage', 'createEnhancedSmartOfficeApp', '✅ 增强SmartOffice应用创建完成');
            return enhancedApp;
        }

        // 异步初始化函数
        async function initializeApp() {
            try {
                // 创建页面级别的日志记录器
                pageLogger = getLogger();
                pageLogger.info('IndexPage', 'moduleInit', '✅ 日志系统初始化成功');
                
                // 统一使用内联资源管理器（移除模块依赖）
                pageLogger.info('IndexPage', 'moduleInit', '🔄 初始化内联资源管理器');
                await initializeInlineResourceManager();
                
                pageLogger.info('IndexPage', 'moduleInit', '✅ 所有核心模块导入成功');
                
                // 设置全局日志访问
                window.getLogger = getLogger;
                window.pageLogger = pageLogger;
                
                // 添加全局调试工具函数
                window.debugTools = {
                    // 导出日志
                    exportLogs: (format = 'text') => {
                        const logger = getLogger();
                        const logs = logger.exportLogs(format);
                        console.log(`📤 日志已导出 (${format}格式):`, logs);
                        return logs;
                    },
                    
                    // 显示性能统计
                    showPerformanceStats: () => {
                        const logger = getLogger();
                        const marks = logger.performanceMarks;
                        const counters = logger.debugCounters;
                        
                        console.group('📊 性能统计');
                        console.log('活跃性能标记:', marks.size);
                        marks.forEach((mark, name) => {
                            console.log(`  ${name}: ${mark.module}.${mark.function}() - 开始时间: ${mark.startTime.toFixed(2)}ms`);
                        });
                        
                        console.log('\n调试计数器:', counters.size);
                        counters.forEach((count, name) => {
                            console.log(`  ${name}: ${count}`);
                        });
                        console.groupEnd();
                    },
                    
                    // 显示所有可用命令
                    help: () => {
                        console.group('🛠️ SmartOffice 调试工具');
                        console.log('📊 日志和性能:');
                        console.log('  debugTools.exportLogs()       - 导出日志');
                        console.log('  debugTools.showPerformanceStats() - 显示性能统计');
                        console.log('  pageLogger.info("Module", "function", "message", data) - 记录日志');
                        
                        console.log('\n🔧 日志级别:');
                        console.log('  pageLogger.trace()  - 跟踪信息');
                        console.log('  pageLogger.debug()  - 调试信息');
                        console.log('  pageLogger.info()   - 一般信息');
                        console.log('  pageLogger.warn()   - 警告信息');
                        console.log('  pageLogger.error()  - 错误信息');
                        
                        console.log('\n📈 性能监控:');
                        console.log('  pageLogger.startPerformanceMark("name", "Module", "function")');
                        console.log('  pageLogger.endPerformanceMark("name", "Module", "function")');
                        console.log('  pageLogger.incrementCounter("name", "Module")');
                        
                        console.log('\n🎯 实例:');
                        console.log('  pageLogger.info("Test", "demo", "这是一条测试日志", {test: true});');
                        console.groupEnd();
                    }
                };
                
                // 显示初始化帮助信息
                console.log('🎉 SmartOffice 2.0 调试工具已就绪！输入 debugTools.help() 查看所有可用命令');
                
                pageLogger.info('IndexPage', 'moduleLoad', '页面模块加载完成，等待DOM就绪');
                
                // 模块加载成功，返回true
                return true;
                
            } catch (error) {
                console.error('❌ 模块导入失败:', error);
                console.error('错误详情:', error.message);
                console.error('错误堆栈:', error.stack);
                
                // 显示用户友好的错误信息
                document.body.innerHTML = `
                    <div style="padding: 20px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 20px; color: #721c24;">
                        <h2>❌ 系统初始化失败</h2>
                        <p><strong>错误原因:</strong> ${error.message}</p>
                        <p><strong>可能的解决方案:</strong></p>
                        <ul>
                            <li>检查网络连接</li>
                            <li>刷新页面重试</li>
                            <li>检查浏览器控制台获取详细错误信息</li>
                            <li>确保浏览器支持ES6模块</li>
                        </ul>
                        <button onclick="location.reload()" style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-top: 10px;">
                            重新加载页面
                        </button>
                    </div>
                `;
                
                // 模块加载失败，返回false
                return false;
            }
        }

        // 开始初始化
        initializeApp().then(success => {
            if (success) {
                // 应用初始化
                document.addEventListener('DOMContentLoaded', async () => {
                    const pageStartTime = performance.now();
                    pageLogger.startPerformanceMark('page_initialization', 'IndexPage', 'DOMContentLoaded');
                    
                    try {
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '🚀 正在初始化 SmartOffice 2.0...');
                        pageLogger.debug('IndexPage', 'DOMContentLoaded', 'DOM加载完成，开始应用初始化');
                        
                        // 检查必要的依赖
                        pageLogger.debug('IndexPage', 'DOMContentLoaded', '检查外部依赖库');
                        const dependencies = {
                            html2canvas: typeof html2canvas !== 'undefined',
                            jsPDF: typeof window.jspdf !== 'undefined',
                            ImageBase64: typeof window.ImageBase64 !== 'undefined'
                        };
                        
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '依赖检查结果', dependencies);
                        
                        // 记录初始化参数
                        const initConfig = {
                            autoInit: true,
                            enablePlugins: true,
                            enableWorkflow: true,
                            defaultLanguage: 'zh-CN',
                            defaultTheme: 'default',
                            defaultDocumentType: 'receipt',
                            logLevel: LogLevel.DEBUG,
                            enableDebugPanel: false,  // 可设置为true启用调试面板
                            
                            // UI配置
                            ui: {
                                showToolbar: true,
                                showSidebar: true,
                                showStatusBar: true,
                                enableKeyboardShortcuts: true
                            },
                            
                            // 导出配置
                            export: {
                                defaultFormat: 'pdf',
                                autoDownload: true,
                                showProgress: true,
                                enableBatch: true
                            }
                        };
                        
                        pageLogger.debug('IndexPage', 'DOMContentLoaded', '应用初始化配置', initConfig);
                        
                        // 统一使用增强内联架构（移除模块依赖）
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '🌐 使用统一增强内联架构');
                        
                        // 创建增强的内联SmartOffice应用
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '创建增强SmartOffice应用（内联模式）');
                        pageLogger.startPerformanceMark('enhanced_app_creation', 'IndexPage', 'createEnhancedApp');
                        
                        const app = await createEnhancedSmartOfficeApp(initConfig);
                        
                        const enhancedDuration = pageLogger.endPerformanceMark('enhanced_app_creation', 'IndexPage', 'createEnhancedApp');
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '增强应用创建完成', {
                            duration: `${enhancedDuration?.toFixed(2)}ms`
                        });
                        
                        // 初始化应用（包括统一渲染架构）
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '🔧 初始化应用核心组件');
                        pageLogger.startPerformanceMark('app_initialization', 'IndexPage', 'appInit');
                        
                        await app.init();
                        
                        const initDuration = pageLogger.endPerformanceMark('app_initialization', 'IndexPage', 'appInit');
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '✅ 应用初始化完成', {
                            duration: `${initDuration?.toFixed(2)}ms`
                        });
                        
                        // 设置为全局默认应用
                        pageLogger.debug('IndexPage', 'DOMContentLoaded', '设置全局应用引用');
                        window.smartOffice = app;
                        window.smartOfficeApp = app; // 兼容性引用
                        
                        // 记录应用信息
                        const appInfo = app.getAppInfo();
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '应用信息获取完成', appInfo);
                        
                        // 应用初始化成功
                        const pageInitDuration = pageLogger.endPerformanceMark('page_initialization', 'IndexPage', 'DOMContentLoaded');
                        const totalPageTime = performance.now() - pageStartTime;
                        
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '✅ SmartOffice 2.0 初始化完成！', {
                            pageInitDuration: `${pageInitDuration?.toFixed(2)}ms`,
                            totalPageTime: `${totalPageTime.toFixed(2)}ms`,
                            appVersion: appInfo.version,
                            managersCount: appInfo.managersCount,
                            debugPanelEnabled: initConfig.enableDebugPanel
                        });
                        
                        // 更新API状态指示器
                        pageLogger.debug('IndexPage', 'DOMContentLoaded', '更新API状态指示器为成功状态');
                        const indicator = document.getElementById('api-status-indicator');
                        if (indicator) {
                            indicator.className = 'text-sm bg-green-700 text-green-100 px-3 py-1 rounded-full flex items-center';
                            indicator.innerHTML = '<span class="w-2 h-2 bg-green-300 rounded-full mr-2"></span>系统就绪';
                        }
                        
                        // 🔧 修复：在应用初始化完成后立即设置API密钥
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '🔧 正在设置API密钥...');
                        updateNLPConfig();
                        
                        // 显示成功消息
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '🎉 SmartOffice 2.0 已成功启动！');
                        
                    } catch (error) {
                        pageLogger.error('IndexPage', 'DOMContentLoaded', '❌ 应用初始化失败', { error: error.message, stack: error.stack });
                        
                        // 更新API状态指示器为错误状态
                        const indicator = document.getElementById('api-status-indicator');
                        if (indicator) {
                            indicator.className = 'text-sm bg-red-700 text-red-100 px-3 py-1 rounded-full flex items-center';
                            indicator.innerHTML = '<span class="w-2 h-2 bg-red-300 rounded-full mr-2"></span>初始化失败';
                        }
                        
                        // 显示错误信息
                        console.error('❌ SmartOffice 2.0 初始化失败:', error);
                        alert(`SmartOffice 2.0 初始化失败: ${error.message}`);
                    }
                });
            } else {
                console.error('❌ 模块加载失败，无法启动应用');
            }
        });

        /**
         * 创建简化的SmartOffice应用 - 保留用于兼容性
         * @function createSimplifiedSmartOfficeApp
         * @param {Object} config - 应用配置
         * @returns {Promise<Object>} 简化的应用实例
         */
        async function createSimplifiedSmartOfficeApp(config = {}) {
            pageLogger.info('IndexPage', 'createSimplifiedSmartOfficeApp', '🔄 创建简化SmartOffice应用');
            
            // 创建简化的应用实例
            const simplifiedApp = {
                config: config,
                isInitialized: false,
                managers: new Map(),
                
                // 应用信息
                getAppInfo() {
                    return {
                        version: '2.0.0-simplified',
                        name: 'SmartOffice (Simplified)',
                        mode: 'file-protocol',
                        managersCount: this.managers.size,
                        isInitialized: this.isInitialized,
                        timestamp: new Date().toISOString(),
                        features: {
                            nlp: true,
                            export: 'limited',
                            offline: true,
                            modules: 'inline'
                        }
                    };
                },
                
                // 获取管理器
                getManager(managerName) {
                    if (this.managers.has(managerName)) {
                        return this.managers.get(managerName);
                    }
                    
                    // 创建简化的管理器
                    switch (managerName) {
                        case 'nlp':
                            return this.createSimplifiedNLPManager();
                        case 'offlineConfig':
                            return this.createSimplifiedConfigManager();
                        case 'resourceManager':
                            return window.inlineResourceManager;
                        default:
                            pageLogger.warn('SimplifiedApp', 'getManager', '未知管理器', { managerName });
                            return null;
                    }
                },
                
                // 创建简化的NLP管理器
                createSimplifiedNLPManager() {
                    if (this.managers.has('nlp')) {
                        return this.managers.get('nlp');
                    }
                    
                    const nlpManager = {
                        async processText(text, documentType = 'receipt') {
                            pageLogger.info('SimplifiedNLP', 'processText', '🧠 处理文本（简化模式）', {
                                textLength: text.length,
                                documentType
                            });
                            
                            // 简化的文本处理逻辑
                            const extractedData = this.extractBasicInfo(text);
                            
                            return {
                                confidence: 0.7,
                                extractedData: extractedData,
                                mode: 'simplified',
                                timestamp: new Date().toISOString()
                            };
                        },
                        
                        async processImage(image, text = '', documentType = 'receipt') {
                            pageLogger.warn('SimplifiedNLP', 'processImage', '⚠️ 图片处理在简化模式下不可用');
                            // 在简化模式下，只处理文本部分
                            return this.processText(text, documentType);
                        },
                        
                        extractBasicInfo(text) {
                            const data = {};
                            
                            // 提取金额
                            const amountMatch = text.match(/(\d+(?:\.\d{2})?)\s*(?:令吉|RM|元|RMB)/i);
                            if (amountMatch) {
                                data.totalAmount = parseFloat(amountMatch[1]);
                            }
                            
                            // 提取姓名
                            const nameMatch = text.match(/([张李王刘陈杨黄赵吴周徐孙马朱胡郭何高林罗郑梁谢宋唐许韩冯邓曹彭曾萧田董袁潘于蒋蔡余杜叶程苏魏吕丁任沈姚卢姜崔钟谭陆汪范金石廖贾夏韦付方白邹孟熊秦邱江尹薛闫段雷侯龙史陶黎贺顾毛郝龚邵万钱严覃武戴莫孔向汤]\w*)/);
                            if (nameMatch) {
                                data.customerName = nameMatch[1];
                            }
                            
                            // 提取数量
                            const quantityMatch = text.match(/(\d+)\s*(?:个|件|晚|天|次)/);
                            if (quantityMatch) {
                                data.quantities = [parseInt(quantityMatch[1])];
                            }
                            
                            // 提取服务项目
                            const serviceMatch = text.match(/(酒店|接机|送机|包车|导游|门票|餐饮|住宿|交通)/g);
                            if (serviceMatch) {
                                data.services = serviceMatch;
                            }
                            
                            // 提取日期
                            const dateMatch = text.match(/(\d{4}[-\/]\d{1,2}[-\/]\d{1,2})/);
                            if (dateMatch) {
                                data.issueDate = dateMatch[1];
                            }
                            
                            return data;
                        }
                    };
                    
                    this.managers.set('nlp', nlpManager);
                    return nlpManager;
                },
                
                // 创建简化的配置管理器
                createSimplifiedConfigManager() {
                    if (this.managers.has('offlineConfig')) {
                        return this.managers.get('offlineConfig');
                    }
                    
                    const configManager = {
                        config: {
                            mode: 'simplified',
                            nlp: {
                                enabled: true,
                                mode: 'local'
                            }
                        },
                        
                        get(path, defaultValue = undefined) {
                            const keys = path.split('.');
                            let value = this.config;
                            
                            for (const key of keys) {
                                if (value && typeof value === 'object' && key in value) {
                                    value = value[key];
                                } else {
                                    return defaultValue;
                                }
                            }
                            
                            return value;
                        },
                        
                        set(path, value) {
                            const keys = path.split('.');
                            let current = this.config;
                            
                            for (let i = 0; i < keys.length - 1; i++) {
                                const key = keys[i];
                                if (!(key in current) || typeof current[key] !== 'object') {
                                    current[key] = {};
                                }
                                current = current[key];
                            }
                            
                            current[keys[keys.length - 1]] = value;
                        },
                        
                        isOfflineMode() {
                            return true;
                        }
                    };
                    
                    this.managers.set('offlineConfig', configManager);
                    return configManager;
                },
                
                // 初始化应用
                async initialize() {
                    pageLogger.info('SimplifiedApp', 'initialize', '🔄 初始化简化应用');
                    
                    // 创建基础管理器
                    this.createSimplifiedNLPManager();
                    this.createSimplifiedConfigManager();
                    
                    this.isInitialized = true;
                    pageLogger.info('SimplifiedApp', 'initialize', '✅ 简化应用初始化完成');
                    
                    return this;
                },
                
                // 销毁应用
                async destroy() {
                    pageLogger.info('SimplifiedApp', 'destroy', '🔄 销毁简化应用');
                    this.managers.clear();
                    this.isInitialized = false;
                }
            };
            
            // 初始化应用
            await simplifiedApp.initialize();
            
            pageLogger.info('IndexPage', 'createSimplifiedSmartOfficeApp', '✅ 简化SmartOffice应用创建完成');
            return simplifiedApp;
        }
        
        // 开始初始化
        initializeApp().then(success => {
            if (success) {
                // 应用初始化
                document.addEventListener('DOMContentLoaded', async () => {
                    const pageStartTime = performance.now();
                    pageLogger.startPerformanceMark('page_initialization', 'IndexPage', 'DOMContentLoaded');
                    
                    try {
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '🚀 正在初始化 SmartOffice 2.0...');
                        pageLogger.debug('IndexPage', 'DOMContentLoaded', 'DOM加载完成，开始应用初始化');
                        
                        // 检查必要的依赖
                        pageLogger.debug('IndexPage', 'DOMContentLoaded', '检查外部依赖库');
                        const dependencies = {
                            html2canvas: typeof html2canvas !== 'undefined',
                            jsPDF: typeof window.jspdf !== 'undefined',
                            ImageBase64: typeof window.ImageBase64 !== 'undefined'
                        };
                        
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '依赖检查结果', dependencies);
                        
                        // 记录初始化参数
                        const initConfig = {
                            autoInit: true,
                            enablePlugins: true,
                            enableWorkflow: true,
                            defaultLanguage: 'zh-CN',
                            defaultTheme: 'default',
                            defaultDocumentType: 'receipt',
                            logLevel: LogLevel.DEBUG,
                            enableDebugPanel: false,  // 可设置为true启用调试面板
                            
                            // UI配置
                            ui: {
                                showToolbar: true,
                                showSidebar: true,
                                showStatusBar: true,
                                enableKeyboardShortcuts: true
                            },
                            
                            // 导出配置
                            export: {
                                defaultFormat: 'pdf',
                                autoDownload: true,
                                showProgress: true,
                                enableBatch: true
                            }
                        };
                        
                        pageLogger.debug('IndexPage', 'DOMContentLoaded', '应用初始化配置', initConfig);
                        
                        // 统一使用增强内联架构（移除模块依赖）
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '🌐 使用统一增强内联架构');
                        
                        // 创建增强的内联SmartOffice应用
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '创建增强SmartOffice应用（内联模式）');
                        pageLogger.startPerformanceMark('enhanced_app_creation', 'IndexPage', 'createEnhancedApp');
                        
                        const app = await createEnhancedSmartOfficeApp(initConfig);
                        
                        const enhancedDuration = pageLogger.endPerformanceMark('enhanced_app_creation', 'IndexPage', 'createEnhancedApp');
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '增强应用创建完成', {
                            duration: `${enhancedDuration?.toFixed(2)}ms`
                        });
                        
                        // 初始化应用（包括统一渲染架构）
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '🔧 初始化应用核心组件');
                        pageLogger.startPerformanceMark('app_initialization', 'IndexPage', 'appInit');
                        
                        await app.init();
                        
                        const initDuration = pageLogger.endPerformanceMark('app_initialization', 'IndexPage', 'appInit');
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '✅ 应用初始化完成', {
                            duration: `${initDuration?.toFixed(2)}ms`
                        });
                        
                        // 设置为全局默认应用
                        pageLogger.debug('IndexPage', 'DOMContentLoaded', '设置全局应用引用');
                        window.smartOffice = app;
                        window.smartOfficeApp = app; // 兼容性引用
                        
                        // 记录应用信息
                        const appInfo = app.getAppInfo();
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '应用信息获取完成', appInfo);
                        
                        // 应用初始化成功
                        const pageInitDuration = pageLogger.endPerformanceMark('page_initialization', 'IndexPage', 'DOMContentLoaded');
                        const totalPageTime = performance.now() - pageStartTime;
                        
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '✅ SmartOffice 2.0 初始化完成！', {
                            pageInitDuration: `${pageInitDuration?.toFixed(2)}ms`,
                            totalPageTime: `${totalPageTime.toFixed(2)}ms`,
                            appVersion: appInfo.version,
                            managersCount: appInfo.managersCount,
                            debugPanelEnabled: initConfig.enableDebugPanel
                        });
                        
                        // 更新API状态指示器
                        pageLogger.debug('IndexPage', 'DOMContentLoaded', '更新API状态指示器为成功状态');
                        const indicator = document.getElementById('api-status-indicator');
                        if (indicator) {
                            indicator.className = 'text-sm bg-green-700 text-green-100 px-3 py-1 rounded-full flex items-center';
                            indicator.innerHTML = `
                                <span class="flex h-2 w-2 relative mr-2">
                                    <span class="relative inline-flex rounded-full h-2 w-2 bg-green-400"></span>
                                </span>
                                <span class="api-status-text">系统就绪</span>
                            `;
                            pageLogger.trace('IndexPage', 'DOMContentLoaded', 'API状态指示器更新完成');
                        } else {
                            pageLogger.warn('IndexPage', 'DOMContentLoaded', 'API状态指示器元素未找到');
                        }
                        
                        // 🔧 修复：在应用初始化完成后立即设置API密钥
                        pageLogger.info('IndexPage', 'DOMContentLoaded', '🔧 正在设置API密钥...');
                        updateNLPConfig();
                        
                        // 设置兼容性事件监听
                        pageLogger.debug('IndexPage', 'DOMContentLoaded', '开始设置兼容性事件处理器');
                        setupLegacyEventHandlers(app);
                        
                        // 记录成功统计
                        pageLogger.incrementCounter('successful_initializations', 'IndexPage');
                        
                        // 触发自定义初始化完成事件
                        pageLogger.debug('IndexPage', 'DOMContentLoaded', '触发自定义初始化完成事件');
                        window.dispatchEvent(new CustomEvent('smartoffice:ready', {
                            detail: {
                                app: app,
                                initTime: totalPageTime,
                                timestamp: new Date().toISOString()
                            }
                        }));
                        
                    } catch (error) {
                        const pageInitDuration = pageLogger.endPerformanceMark('page_initialization', 'IndexPage', 'DOMContentLoaded');
                        
                        pageLogger.error('IndexPage', 'DOMContentLoaded', '❌ SmartOffice 初始化失败', {
                            error: error.message,
                            stack: error.stack,
                            initDuration: pageInitDuration
                        });
                        
                        // 更新API状态指示器显示错误
                        pageLogger.debug('IndexPage', 'DOMContentLoaded', '更新API状态指示器为错误状态');
                        const indicator = document.getElementById('api-status-indicator');
                        if (indicator) {
                            indicator.className = 'text-sm bg-red-700 text-red-100 px-3 py-1 rounded-full flex items-center';
                            indicator.innerHTML = `
                                <span class="flex h-2 w-2 relative mr-2">
                                    <span class="relative inline-flex rounded-full h-2 w-2 bg-red-400"></span>
                                </span>
                                <span class="api-status-text">初始化失败</span>
                            `;
                            pageLogger.trace('IndexPage', 'DOMContentLoaded', 'API状态指示器错误状态更新完成');
                        } else {
                            pageLogger.warn('IndexPage', 'DOMContentLoaded', 'API状态指示器元素未找到');
                        }
                        
                        // 显示用户友好的错误信息
                        pageLogger.debug('IndexPage', 'DOMContentLoaded', '显示用户错误通知');
                        showErrorNotification('系统初始化失败', error.message);
                        
                        // 记录失败统计
                        pageLogger.incrementCounter('failed_initializations', 'IndexPage');
                        
                        // 触发错误事件
                        window.dispatchEvent(new CustomEvent('smartoffice:error', {
                            detail: {
                                error: error,
                                phase: 'initialization',
                                timestamp: new Date().toISOString()
                            }
                        }));
                    }
                });
            }
        });

        /**
         * 根据文档类型更新预览和表单字段的显示
         * @function updatePreviewAndFormFields
         * @param {string} documentType - 当前选择的文档类型
         */
        function updatePreviewAndFormFields(documentType) {
            pageLogger.info('IndexPage', 'updatePreviewAndFormFields', `🔄 更新预览和表单字段，类型: ${documentType}`);

            // 1. 更新预览模板的逻辑
            if (window.smartOfficeApp && window.smartOfficeApp.getManager) {
                const rendererManager = window.smartOfficeApp.getManager('renderers');
                if (rendererManager && rendererManager.switchTemplate) {
                    rendererManager.switchTemplate(documentType);
                    pageLogger.debug('IndexPage', 'updatePreviewAndFormFields', `预览模板已切换到: ${documentType}`);
                } else {
                    pageLogger.warn('IndexPage', 'updatePreviewAndFormFields', '渲染器管理器或switchTemplate方法未找到');
                }
            } else {
                pageLogger.warn('IndexPage', 'updatePreviewAndFormFields', 'SmartOffice应用实例未找到');
            }

            // 2. 控制特定字段面板的显示/隐藏
            const invoicePanel = document.getElementById('invoice-specific-panel');
            const quotationPanel = document.getElementById('quotation-specific-panel');
            // 未来可能还有其他类型的面板，比如 driver-agreement-specific-panel

            if (invoicePanel) {
                invoicePanel.classList.toggle('hidden-panel', documentType !== 'invoice');
                invoicePanel.classList.toggle('hidden', documentType !== 'invoice'); // 确保Tailwind的hidden也同步
            }
            if (quotationPanel) {
                quotationPanel.classList.toggle('hidden-panel', documentType !== 'quotation');
                quotationPanel.classList.toggle('hidden', documentType !== 'quotation'); // 确保Tailwind的hidden也同步
            }

            // 根据文档类型更新预览区域的标题等元素
            const documentTitleElement = document.getElementById('document-title');
            const documentTitleInvoiceElement = document.getElementById('document-title-invoice');
            const documentTitleQuotationElement = document.getElementById('document-title-quotation');
            const documentNumberElement = document.getElementById('document-number');
            const documentNumberInvoiceElement = document.getElementById('document-number-invoice');
            const documentNumberQuotationElement = document.getElementById('document-number-quotation');
            const paymentMethodSection = document.querySelector('.payment-method-section');
             const driverAgreementPreviewArea = document.getElementById('specific-driver-agreement-render-area');
            const standardPreviewArea = document.getElementById('document-container');


            // 先隐藏所有特定标题和编号
            if(documentTitleElement) documentTitleElement.classList.add('hidden');
            if(documentTitleInvoiceElement) documentTitleInvoiceElement.classList.add('hidden');
            if(documentTitleQuotationElement) documentTitleQuotationElement.classList.add('hidden');
            if(documentNumberElement) documentNumberElement.classList.add('hidden');
            if(documentNumberInvoiceElement) documentNumberInvoiceElement.classList.add('hidden');
            if(documentNumberQuotationElement) documentNumberQuotationElement.classList.add('hidden');
            if(paymentMethodSection) paymentMethodSection.classList.add('hidden');

            // 根据类型显示对应的标题和编号
            if (documentType === 'receipt') {
                if(documentTitleElement) documentTitleElement.classList.remove('hidden');
                if(documentNumberElement) documentNumberElement.classList.remove('hidden');
                if(paymentMethodSection) paymentMethodSection.classList.remove('hidden');
                if(driverAgreementPreviewArea) driverAgreementPreviewArea.classList.add('hidden');
                if(standardPreviewArea) standardPreviewArea.classList.remove('hidden');
            } else if (documentType === 'invoice') {
                if(documentTitleInvoiceElement) documentTitleInvoiceElement.classList.remove('hidden');
                if(documentNumberInvoiceElement) documentNumberInvoiceElement.classList.remove('hidden');
                if(driverAgreementPreviewArea) driverAgreementPreviewArea.classList.add('hidden');
                if(standardPreviewArea) standardPreviewArea.classList.remove('hidden');
            } else if (documentType === 'quotation') {
                if(documentTitleQuotationElement) documentTitleQuotationElement.classList.remove('hidden');
                if(documentNumberQuotationElement) documentNumberQuotationElement.classList.remove('hidden');
                if(driverAgreementPreviewArea) driverAgreementPreviewArea.classList.add('hidden');
                if(standardPreviewArea) standardPreviewArea.classList.remove('hidden');
            } else if (documentType === 'driver_agreement') {
                // 司机协议有单独的预览区域
                if(driverAgreementPreviewArea) driverAgreementPreviewArea.classList.remove('hidden');
                if(standardPreviewArea) standardPreviewArea.classList.add('hidden');
                // 司机协议可能不需要显示单号和通用标题，或者有其特定逻辑
            }
            
            // 手动触发一次更新预览的逻辑，确保切换后预览正确
            if (typeof window.updateDocumentPreview === 'function') {
                window.updateDocumentPreview();
                pageLogger.debug('IndexPage', 'updatePreviewAndFormFields', '触发 updateDocumentPreview');
            } else if (window.smartOfficeApp && window.smartOfficeApp.previewDocument) {
                // 使用SmartOffice应用的预览方法作为备选
                try {
                    const currentContent = window.smartOfficeApp._getCurrentDocumentContent();
                    window.smartOfficeApp.previewDocument(currentContent);
                    pageLogger.debug('IndexPage', 'updatePreviewAndFormFields', '使用SmartOffice预览方法更新');
                } catch (error) {
                    pageLogger.error('IndexPage', 'updatePreviewAndFormFields', '预览更新失败', error);
                }
            } else {
                pageLogger.warn('IndexPage', 'updatePreviewAndFormFields', '未找到可用的预览更新方法');
            }


            pageLogger.info('IndexPage', 'updatePreviewAndFormFields', '表单字段和预览更新完成');
        }

        // 全局Gemini NLP处理器访问函数
        window.getGeminiNLPProcessor = function(options = {}) {
            if (window.smartOfficeApp && window.smartOfficeApp.getGeminiNLPProcessor) {
                return window.smartOfficeApp.getGeminiNLPProcessor(options);
            } else {
                pageLogger.warn('IndexPage', 'getGeminiNLPProcessor', 'SmartOffice应用未初始化，无法获取Gemini处理器');
                return null;
            }
        };

        /**
         * 更新公司图片显示
         * @function updateCompanyImages
         * @param {string} companyCode - 公司代码
         */
        function updateCompanyImages(companyCode) {
            pageLogger.info('IndexPage', 'updateCompanyImages', '🖼️ 更新公司图片', { companyCode });
            console.log('[updateCompanyImages] companyCode:', companyCode); // 新增日志
            
            try {
                // 获取图片数据
                const logoData = window.ImageBase64 ? window.ImageBase64.getLogo(companyCode) : '';
                const stampData = window.ImageBase64 ? window.ImageBase64.getStamp(companyCode) : '';
                const footerData = window.ImageBase64 ? window.ImageBase64.getFooter(companyCode) : '';
                console.log('[updateCompanyImages] footerData (length):', footerData ? footerData.length : 'empty'); // 新增日志
                
                // 更新页眉Logo
                const companyLogo = document.getElementById('company-logo');
                if (companyLogo && logoData) {
                    companyLogo.src = logoData;
                    companyLogo.setAttribute('data-company', companyCode);
                    pageLogger.debug('IndexPage', 'updateCompanyImages', '✅ 页眉Logo已更新');
                }
                
                // 更新印章
                const stampContainers = document.querySelectorAll('.company-stamp');
                stampContainers.forEach(container => {
                    // 清除现有印章
                    container.innerHTML = '';
                    
                    if (stampData) {
                        const stampImg = document.createElement('img');
                        stampImg.src = stampData;
                        stampImg.alt = '公司印章';
                        stampImg.className = 'stamp-image';
                        stampImg.setAttribute('data-company', companyCode);
                        container.appendChild(stampImg);
                        pageLogger.debug('IndexPage', 'updateCompanyImages', '✅ 印章已更新');
                    }
                });
                
                // 更新页脚图片
                const footerContainers = document.querySelectorAll('.unified-document-footer.company-footer-image-container');
                console.log('[updateCompanyImages] footerContainers found:', footerContainers.length); // 新增日志
                footerContainers.forEach(container => {
                    if (footerData) {
                        let footerImg = container.querySelector('img');
                        if (!footerImg) {
                            footerImg = document.createElement('img');
                            footerImg.className = 'footer-image';
                            container.appendChild(footerImg);
                        }
                        footerImg.src = footerData;
                        footerImg.alt = '公司页脚';
                        footerImg.setAttribute('data-company', companyCode);
                        pageLogger.debug('IndexPage', 'updateCompanyImages', '✅ 页脚图片已更新');
                    } else {
                        // 新增日志: 如果footerData为空，也清除现有图片
                        let footerImg = container.querySelector('img');
                        if (footerImg) {
                            container.removeChild(footerImg);
                        }
                        pageLogger.warn('IndexPage', 'updateCompanyImages', '⚠️ 页脚数据为空，已清除旧图片', { companyCode });
                    }
                });
                
                pageLogger.info('IndexPage', 'updateCompanyImages', '✅ 公司图片更新完成', { 
                    companyCode,
                    hasLogo: !!logoData,
                    hasStamp: !!stampData,
                    hasFooter: !!footerData
                });
                
            } catch (error) {
                pageLogger.error('IndexPage', 'updateCompanyImages', '❌ 公司图片更新失败', { 
                    error: error.message,
                    companyCode 
                });
            }
        }

        /**
         * 测试公司配置和图片显示对应关系
         * @function testCompanyImageMapping
         */
        function testCompanyImageMapping() {
            pageLogger.info('IndexPage', 'testCompanyImageMapping', '🧪 开始测试公司配置和图片显示对应关系');
            
            const companies = ['sky-mirror', 'gomyhire'];
            const testResults = {};
            
            companies.forEach(companyCode => {
                pageLogger.info('IndexPage', 'testCompanyImageMapping', `测试公司: ${companyCode}`);
                
                const result = {
                    companyCode,
                    logo: null,
                    stamp: null,
                    footer: null,
                    errors: []
                };
                
                try {
                    // 测试Logo
                    if (window.ImageBase64 && window.ImageBase64.getLogo) {
                        result.logo = window.ImageBase64.getLogo(companyCode);
                        pageLogger.debug('IndexPage', 'testCompanyImageMapping', `Logo数据长度: ${result.logo ? result.logo.length : 0}`);
                    } else {
                        result.errors.push('ImageBase64.getLogo 方法不可用');
                    }
                    
                    // 测试印章
                    if (window.ImageBase64 && window.ImageBase64.getStamp) {
                        result.stamp = window.ImageBase64.getStamp(companyCode);
                        pageLogger.debug('IndexPage', 'testCompanyImageMapping', `印章数据长度: ${result.stamp ? result.stamp.length : 0}`);
                    } else {
                        result.errors.push('ImageBase64.getStamp 方法不可用');
                    }
                    
                    // 测试页脚
                    if (window.ImageBase64 && window.ImageBase64.getFooter) {
                        result.footer = window.ImageBase64.getFooter(companyCode);
                        pageLogger.debug('IndexPage', 'testCompanyImageMapping', `页脚数据长度: ${result.footer ? result.footer.length : 0}`);
                    } else {
                        result.errors.push('ImageBase64.getFooter 方法不可用');
                    }
                    
                } catch (error) {
                    result.errors.push(`测试失败: ${error.message}`);
                    pageLogger.error('IndexPage', 'testCompanyImageMapping', `公司 ${companyCode} 测试失败`, { error: error.message });
                }
                
                testResults[companyCode] = result;
            });
            
            // 输出测试结果
            pageLogger.info('IndexPage', 'testCompanyImageMapping', '📊 测试结果汇总', testResults);
            
            // 检查DOM元素
            const domElements = {
                companyLogo: document.getElementById('company-logo'),
                stampContainers: document.querySelectorAll('.company-stamp'),
                footerContainers: document.querySelectorAll('.company-footer-image-container'),
                companySelector: document.getElementById('company-selector')
            };
            
            pageLogger.info('IndexPage', 'testCompanyImageMapping', '🔍 DOM元素检查', {
                hasCompanyLogo: !!domElements.companyLogo,
                stampContainerCount: domElements.stampContainers.length,
                footerContainerCount: domElements.footerContainers.length,
                hasCompanySelector: !!domElements.companySelector,
                currentCompany: domElements.companySelector ? domElements.companySelector.value : 'unknown'
            });
            
            return testResults;
        }

        // 添加公司选择器事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            const companySelector = document.getElementById('company-selector');
            if (companySelector) {
                companySelector.addEventListener('change', function(event) {
                    const selectedCompany = event.target.value;
                    pageLogger.info('IndexPage', 'companySelector.change', '🏢 公司选择变更', { selectedCompany });
                    
                    // 更新公司图片
                    updateCompanyImages(selectedCompany);
                    
                    // 触发预览更新
                    if (typeof window.updateDocumentPreview === 'function') {
                        window.updateDocumentPreview();
                    }
                });
                
                // 初始化时设置默认公司图片
                const initialCompany = companySelector.value || 'gomyhire';
                updateCompanyImages(initialCompany);
                
                pageLogger.info('IndexPage', 'DOMContentLoaded', '✅ 公司选择器事件监听器已设置', { initialCompany });
            } else {
                pageLogger.warn('IndexPage', 'DOMContentLoaded', '⚠️ 公司选择器元素未找到');
            }
        });

        // 暴露测试函数到全局
        window.testCompanyImageMapping = testCompanyImageMapping;
        window.updateCompanyImages = updateCompanyImages;

        /**
         * @function 打印范围指示器管理器
         * @description 管理预览模块中的打印范围显示功能，确保用户能清楚看到实际打印区域
         */
        class PrintRangeIndicatorManager {
            constructor() {
                this.isVisible = false; // 打印范围指示器是否可见
                this.indicator = null; // 打印范围指示器元素
                this.toggleButton = null; // 切换按钮元素
                this.init();
            }

            /**
             * @function 初始化打印范围指示器管理器
             * @description 设置事件监听器和初始状态
             */
            init() {
                // 获取DOM元素
                this.indicator = document.getElementById('print-range-indicator');
                this.toggleButton = document.getElementById('print-range-toggle-btn');
                
                if (!this.indicator || !this.toggleButton) {
                    console.warn('打印范围指示器元素未找到');
                    return;
                }

                // 设置切换按钮事件监听器
                this.toggleButton.addEventListener('click', () => {
                    this.toggle();
                });

                // 键盘快捷键支持 (Ctrl+Shift+P 显示打印范围)
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey && e.shiftKey && e.key === 'P') {
                        e.preventDefault();
                        this.toggle();
                    }
                });

                // 初始化状态
                this.updateButtonState();
                
                // 监听文档类型变化
                this.watchDocumentTypeChanges();
                
                // 初始同步导出设置
                this.syncWithExportSettings();
                
                pageLogger.info('IndexPage', 'PrintRangeIndicatorManager.init', '✅ 打印范围指示器管理器初始化完成');
            }

            /**
             * @function 切换打印范围指示器显示状态
             * @description 显示或隐藏打印范围指示器
             */
            toggle() {
                this.isVisible = !this.isVisible;
                this.updateDisplay();
                this.updateButtonState();
                
                // 记录用户操作
                pageLogger.info('IndexPage', 'PrintRangeIndicatorManager.toggle', `🖨️ 打印范围指示器${this.isVisible ? '显示' : '隐藏'}`);
            }

            /**
             * @function 显示打印范围指示器
             * @description 显示所有打印范围相关的视觉元素
             */
            show() {
                this.isVisible = true;
                this.updateDisplay();
                this.updateButtonState();
                // 显示时同步最新的导出设置
                this.syncWithExportSettings();
            }

            /**
             * @function 隐藏打印范围指示器
             * @description 隐藏所有打印范围相关的视觉元素
             */
            hide() {
                this.isVisible = false;
                this.updateDisplay();
                this.updateButtonState();
            }

            /**
             * @function 更新显示状态
             * @description 根据当前状态更新指示器的显示
             * @private
             */
            updateDisplay() {
                if (!this.indicator) return;

                if (this.isVisible) {
                    this.indicator.classList.remove('hidden');
                    this.indicator.classList.add('visible');
                } else {
                    this.indicator.classList.add('hidden');
                    this.indicator.classList.remove('visible');
                }
            }

            /**
             * @function 更新按钮状态
             * @description 更新切换按钮的外观和文本
             * @private
             */
            updateButtonState() {
                if (!this.toggleButton) return;

                if (this.isVisible) {
                    this.toggleButton.classList.add('bg-blue-600', 'text-white');
                    this.toggleButton.classList.remove('bg-blue-100', 'text-blue-600');
                    this.toggleButton.innerHTML = '<i class="fas fa-eye-slash mr-1"></i>隐藏范围';
                    this.toggleButton.title = '隐藏打印范围指示器 (Ctrl+Shift+P)';
                } else {
                    this.toggleButton.classList.remove('bg-blue-600', 'text-white');
                    this.toggleButton.classList.add('bg-blue-100', 'text-blue-600');
                    this.toggleButton.innerHTML = '<i class="fas fa-print mr-1"></i>打印范围';
                    this.toggleButton.title = '显示打印范围指示器 (Ctrl+Shift+P)';
                }
            }

            /**
             * @function 更新打印范围信息
             * @description 根据当前文档类型和设置更新打印范围标签信息
             * @param {Object} printSettings - 打印设置对象
             */
            updatePrintInfo(printSettings = {}) {
                if (!this.indicator) return;

                const {
                    paperSize = 'A4',
                    orientation = 'portrait',
                    margins = { top: 20, bottom: 15, left: 10, right: 10 },
                    contentWidth = 718,
                    contentHeight = 991
                } = printSettings;

                // 更新CSS变量以匹配当前设置
                this.updateCSSVariables(margins);

                // 更新边距标签
                const marginLabels = {
                    top: this.indicator.querySelector('.margin-label-top'),
                    bottom: this.indicator.querySelector('.margin-label-bottom'),
                    left: this.indicator.querySelector('.margin-label-left'),
                    right: this.indicator.querySelector('.margin-label-right')
                };

                if (marginLabels.top) marginLabels.top.textContent = `上边距 ${margins.top}mm`;
                if (marginLabels.bottom) marginLabels.bottom.textContent = `下边距 ${margins.bottom}mm`;
                if (marginLabels.left) marginLabels.left.textContent = `左边距 ${margins.left}mm`;
                if (marginLabels.right) marginLabels.right.textContent = `右边距 ${margins.right}mm`;

                // 更新打印区域尺寸标签
                const sizeLabel = this.indicator.querySelector('.print-area-size-label');
                if (sizeLabel) {
                    sizeLabel.textContent = `打印区域: ${contentWidth}×${contentHeight}px`;
                }

                // 更新页面信息标签
                const pageLabel = this.indicator.querySelector('.page-info-label');
                if (pageLabel) {
                    const orientationText = orientation === 'portrait' ? '纵向' : '横向';
                    pageLabel.textContent = `${paperSize} ${orientationText} (210×297mm)`;
                }
            }

            /**
             * @function 更新CSS变量
             * @description 根据打印设置更新CSS变量，确保打印范围指示器正确显示
             * @param {Object} margins - 页边距设置
             * @private
             */
            updateCSSVariables(margins) {
                const documentContainer = document.getElementById('document-container');
                if (!documentContainer) return;

                // 将毫米转换为像素 (96 DPI)
                const mmToPx = (mm) => Math.round(mm * 96 / 25.4);

                // 更新CSS变量
                documentContainer.style.setProperty('--margin-top-px', `${mmToPx(margins.top)}px`);
                documentContainer.style.setProperty('--margin-bottom-px', `${mmToPx(margins.bottom)}px`);
                documentContainer.style.setProperty('--margin-left-px', `${mmToPx(margins.left)}px`);
                documentContainer.style.setProperty('--margin-right-px', `${mmToPx(margins.right)}px`);

                // 计算内容区域尺寸
                const contentWidth = 794 - mmToPx(margins.left) - mmToPx(margins.right); // A4宽度 - 左右边距
                const contentHeight = 1123 - mmToPx(margins.top) - mmToPx(margins.bottom); // A4高度 - 上下边距

                documentContainer.style.setProperty('--content-width-px', `${contentWidth}px`);
                documentContainer.style.setProperty('--content-height-px', `${contentHeight}px`);
            }

            /**
             * @function 获取当前状态
             * @description 返回打印范围指示器的当前状态
             * @returns {Object} 状态对象
             */
            getState() {
                return {
                    isVisible: this.isVisible,
                    hasIndicator: !!this.indicator,
                    hasToggleButton: !!this.toggleButton
                };
            }

            /**
             * @function 同步导出设置
             * @description 从导出管理器获取打印设置并更新打印范围指示器
             */
            syncWithExportSettings() {
                try {
                    // 获取当前文档类型
                    const documentType = this.getCurrentDocumentType();
                    
                    // 获取对应的打印设置
                    const printSettings = this.getExportPrintSettings(documentType);
                    
                    // 更新打印范围信息
                    this.updatePrintInfo(printSettings);
                    
                    pageLogger.debug('IndexPage', 'PrintRangeIndicatorManager.syncWithExportSettings', '✅ 打印设置同步完成', printSettings);
                } catch (error) {
                    pageLogger.warn('IndexPage', 'PrintRangeIndicatorManager.syncWithExportSettings', '⚠️ 打印设置同步失败', { error: error.message });
                }
            }

            /**
             * @function 获取当前文档类型
             * @description 从页面状态获取当前选择的文档类型
             * @returns {string} 文档类型
             * @private
             */
            getCurrentDocumentType() {
                const documentTypeSelector = document.getElementById('document-type');
                return documentTypeSelector ? documentTypeSelector.value : 'receipt';
            }

            /**
             * @function 获取导出打印设置
             * @description 根据文档类型获取对应的打印设置
             * @param {string} documentType - 文档类型
             * @returns {Object} 打印设置对象
             * @private
             */
            getExportPrintSettings(documentType) {
                // 默认A4打印设置
                const defaultSettings = {
                    paperSize: 'A4',
                    orientation: 'portrait',
                    margins: { top: 20, bottom: 15, left: 10, right: 10 },
                    contentWidth: 718,
                    contentHeight: 991
                };

                // 根据文档类型调整设置
                const typeSpecificSettings = {
                    'receipt': {
                        margins: { top: 20, bottom: 15, left: 10, right: 10 },
                        contentWidth: 718,
                        contentHeight: 991
                    },
                    'invoice': {
                        margins: { top: 25, bottom: 20, left: 15, right: 15 },
                        contentWidth: 708,
                        contentHeight: 981
                    },
                    'quotation': {
                        margins: { top: 25, bottom: 20, left: 15, right: 15 },
                        contentWidth: 708,
                        contentHeight: 981
                    },
                    'driver_agreement': {
                        margins: { top: 30, bottom: 25, left: 20, right: 20 },
                        contentWidth: 698,
                        contentHeight: 971
                    }
                };

                return {
                    ...defaultSettings,
                    ...typeSpecificSettings[documentType]
                };
            }

            /**
             * @function 监听文档类型变化
             * @description 监听文档类型选择器的变化，自动同步打印设置
             */
            watchDocumentTypeChanges() {
                const documentTypeSelector = document.getElementById('document-type');
                if (documentTypeSelector) {
                    documentTypeSelector.addEventListener('change', () => {
                        if (this.isVisible) {
                            this.syncWithExportSettings();
                        }
                    });
                }
            }
        }

        /**
         * A4预览缩放控制管理器
         * @function A4PreviewZoomManager
         */
        const A4PreviewZoomManager = {
            currentZoom: 0.75, // 默认75%缩放
            minZoom: 0.3,      // 最小30%
            maxZoom: 1.5,      // 最大150%
            zoomStep: 0.1,     // 每次缩放10%
            
            /**
             * 初始化缩放控制器
             * @function init
             */
            init() {
                pageLogger.info('IndexPage', 'A4PreviewZoomManager.init', '🔍 初始化A4预览缩放控制器');
                
                // 绑定缩放按钮事件
                const zoomInBtn = document.getElementById('zoom-in-button');
                const zoomOutBtn = document.getElementById('zoom-out-button');
                const zoomResetBtn = document.getElementById('zoom-reset-button');
                
                if (zoomInBtn) {
                    zoomInBtn.addEventListener('click', () => this.zoomIn());
                }
                
                if (zoomOutBtn) {
                    zoomOutBtn.addEventListener('click', () => this.zoomOut());
                }
                
                if (zoomResetBtn) {
                    zoomResetBtn.addEventListener('click', () => this.resetZoom());
                }
                
                // 监听窗口大小变化
                window.addEventListener('resize', () => this.handleWindowResize());
                
                // 设置初始缩放
                this.updateZoom();
                
                pageLogger.info('IndexPage', 'A4PreviewZoomManager.init', '✅ A4预览缩放控制器初始化完成');
            },
            
            /**
             * 放大预览
             * @function zoomIn
             */
            zoomIn() {
                if (this.currentZoom < this.maxZoom) {
                    this.currentZoom = Math.min(this.currentZoom + this.zoomStep, this.maxZoom);
                    this.updateZoom();
                    pageLogger.debug('IndexPage', 'A4PreviewZoomManager.zoomIn', `放大到 ${Math.round(this.currentZoom * 100)}%`);
                }
            },
            
            /**
             * 缩小预览
             * @function zoomOut
             */
            zoomOut() {
                if (this.currentZoom > this.minZoom) {
                    this.currentZoom = Math.max(this.currentZoom - this.zoomStep, this.minZoom);
                    this.updateZoom();
                    pageLogger.debug('IndexPage', 'A4PreviewZoomManager.zoomOut', `缩小到 ${Math.round(this.currentZoom * 100)}%`);
                }
            },
            
            /**
             * 重置缩放到默认值
             * @function resetZoom
             */
            resetZoom() {
                this.currentZoom = 0.75;
                this.updateZoom();
                pageLogger.debug('IndexPage', 'A4PreviewZoomManager.resetZoom', '重置缩放到75%');
            },
            
            /**
             * 适应窗口大小
             * @function fitToWindow
             */
            fitToWindow() {
                const previewContainer = document.getElementById('preview-container');
                const documentPreview = document.getElementById('document-preview');
                
                if (previewContainer && documentPreview) {
                    const containerWidth = previewContainer.clientWidth - 40; // 减去padding
                    const a4Width = 794; // A4宽度像素
                    const fitZoom = Math.min(containerWidth / a4Width, 1.0);
                    
                    this.currentZoom = Math.max(fitZoom, this.minZoom);
                    this.updateZoom();
                    pageLogger.debug('IndexPage', 'A4PreviewZoomManager.fitToWindow', `适应窗口缩放到 ${Math.round(this.currentZoom * 100)}%`);
                }
            },
            
            /**
             * 处理窗口大小变化
             * @function handleWindowResize
             */
            handleWindowResize() {
                // 延迟执行以避免频繁调用
                clearTimeout(this.resizeTimeout);
                this.resizeTimeout = setTimeout(() => {
                    this.updateZoom();
                }, 250);
            },
            
            /**
             * 更新预览缩放
             * @function updateZoom
             */
            updateZoom() {
                const documentPreview = document.getElementById('document-preview');
                const zoomLevel = document.getElementById('zoom-level');
                
                if (documentPreview) {
                    // 应用缩放变换
                    documentPreview.style.transform = `scale(${this.currentZoom})`;
                    documentPreview.style.transformOrigin = 'top center';
                    
                    // 调整容器边距以适应缩放
                    const scaledHeight = 1123 * this.currentZoom; // A4高度 * 缩放比例
                    const marginBottom = Math.max(20, (1123 - scaledHeight) / 2);
                    documentPreview.style.marginBottom = `${marginBottom}px`;
                }
                
                if (zoomLevel) {
                    zoomLevel.textContent = `${Math.round(this.currentZoom * 100)}%`;
                }
                
                // 更新按钮状态
                this.updateButtonStates();
            },
            
            /**
             * 更新按钮状态
             * @function updateButtonStates
             */
            updateButtonStates() {
                const zoomInBtn = document.getElementById('zoom-in-button');
                const zoomOutBtn = document.getElementById('zoom-out-button');
                
                if (zoomInBtn) {
                    zoomInBtn.disabled = this.currentZoom >= this.maxZoom;
                    zoomInBtn.style.opacity = this.currentZoom >= this.maxZoom ? '0.5' : '1';
                }
                
                if (zoomOutBtn) {
                    zoomOutBtn.disabled = this.currentZoom <= this.minZoom;
                    zoomOutBtn.style.opacity = this.currentZoom <= this.minZoom ? '0.5' : '1';
                }
            },
            
            /**
             * 获取当前缩放信息
             * @function getZoomInfo
             */
            getZoomInfo() {
                return {
                    current: this.currentZoom,
                    percentage: Math.round(this.currentZoom * 100),
                    min: this.minZoom,
                    max: this.maxZoom
                };
            }
        };

        // 在DOM加载完成后初始化A4缩放管理器和打印范围指示器管理器
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟初始化以确保所有元素都已加载
            setTimeout(() => {
                // 初始化A4缩放管理器
                A4PreviewZoomManager.init();
                
                // 初始化打印范围指示器管理器
                window.printRangeIndicatorManager = new PrintRangeIndicatorManager();
                
                // 初始化导出按钮事件监听器
                initializeExportButtons();
                
                pageLogger.info('IndexPage', 'DOMContentLoaded', '✅ 预览模块管理器初始化完成', {
                    zoomManager: 'A4PreviewZoomManager',
                    printRangeManager: 'PrintRangeIndicatorManager',
                    exportButtons: 'initialized'
                });
            }, 500);
        });

        /**
         * @function 初始化导出按钮事件监听器
         * @description 为导出图片和导出PDF按钮添加事件监听器
         */
        function initializeExportButtons() {
            const exportImageBtn = document.getElementById('export-image-button');
            const exportPdfBtn = document.getElementById('export-pdf-button');
            
            if (exportImageBtn) {
                exportImageBtn.addEventListener('click', () => {
                    pageLogger.info('IndexPage', 'exportImage', '🖼️ 开始导出图片');
                    exportAsImage();
                });
            }
            
            if (exportPdfBtn) {
                exportPdfBtn.addEventListener('click', () => {
                    pageLogger.info('IndexPage', 'exportPDF', '📄 开始导出PDF');
                    exportAsPDF();
                });
            }
            
            pageLogger.info('IndexPage', 'initializeExportButtons', '✅ 导出按钮事件监听器初始化完成');
        }

        /**
         * @function 导出为图片
         * @description 将当前文档预览导出为PNG图片，基于实时预览内容完全导出
         */
        function exportAsImage() {
            try {
                const documentContainer = document.getElementById('document-container');
                if (!documentContainer) {
                    throw new Error('找不到文档容器');
                }

                // 显示导出进度提示
                const exportBtn = document.getElementById('export-image-button');
                const originalText = exportBtn ? exportBtn.textContent : '';
                if (exportBtn) {
                    exportBtn.textContent = '导出中...';
                    exportBtn.disabled = true;
                }

                // 隐藏打印范围指示器和其他非打印元素
                const printRangeIndicator = document.getElementById('print-range-indicator');
                const wasVisible = printRangeIndicator && !printRangeIndicator.classList.contains('hidden');
                if (wasVisible) {
                    printRangeIndicator.classList.add('hidden');
                }

                // 临时移除缩放变换以获得原始尺寸
                const originalTransform = documentContainer.style.transform;
                documentContainer.style.transform = 'scale(1)';

                // 使用html2canvas导出实时预览内容
                if (typeof html2canvas !== 'undefined') {
                    html2canvas(documentContainer, {
                        scale: 2, // 高质量输出
                        useCORS: true,
                        allowTaint: true,
                        backgroundColor: '#ffffff',
                        width: 794, // A4宽度
                        height: 1123, // A4高度
                        scrollX: 0,
                        scrollY: 0
                    }).then(canvas => {
                        // 生成文件名（包含文档类型和时间戳）
                        const docType = getCurrentDocumentType() || 'document';
                        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
                        const filename = `${docType}_${timestamp}.png`;
                        
                        // 创建下载链接并自动下载
                        const link = document.createElement('a');
                        link.download = filename;
                        link.href = canvas.toDataURL('image/png', 1.0); // 最高质量
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        
                        pageLogger.info('IndexPage', 'exportAsImage', '✅ 图片导出成功', { filename });
                        
                        // 显示成功提示
                        showExportSuccessNotification('图片', filename);
                        
                        // 恢复UI状态
                        restoreExportUI();
                    }).catch(error => {
                        pageLogger.error('IndexPage', 'exportAsImage', '❌ 图片导出失败', error);
                        alert('图片导出失败：' + error.message);
                        restoreExportUI();
                    });
                } else {
                    // 简化模式提示
                    pageLogger.warn('IndexPage', 'exportAsImage', '⚠️ html2canvas未加载，使用简化模式');
                    alert('图片导出功能需要完整的html2canvas库。\n当前为简化模式，请使用浏览器的截图功能代替。');
                    restoreExportUI();
                }

                // 恢复UI状态的内部函数
                function restoreExportUI() {
                    // 恢复缩放变换
                    documentContainer.style.transform = originalTransform;
                    
                    // 恢复打印范围指示器
                    if (wasVisible && printRangeIndicator) {
                        printRangeIndicator.classList.remove('hidden');
                    }
                    
                    // 恢复按钮状态
                    if (exportBtn) {
                        exportBtn.textContent = originalText || '导出图片';
                        exportBtn.disabled = false;
                    }
                }
            } catch (error) {
                pageLogger.error('IndexPage', 'exportAsImage', '❌ 图片导出异常', error);
                alert('图片导出失败：' + error.message);
            }
        }

        /**
         * @function 导出为PDF
         * @description 将当前文档预览导出为PDF文件，基于实时预览内容完全导出
         */
        function exportAsPDF() {
            try {
                const documentContainer = document.getElementById('document-container');
                if (!documentContainer) {
                    throw new Error('找不到文档容器');
                }

                // 显示导出进度提示
                const exportBtn = document.getElementById('export-pdf-button');
                const originalText = exportBtn ? exportBtn.textContent : '';
                if (exportBtn) {
                    exportBtn.textContent = '导出中...';
                    exportBtn.disabled = true;
                }

                // 隐藏打印范围指示器和其他非打印元素
                const printRangeIndicator = document.getElementById('print-range-indicator');
                const wasVisible = printRangeIndicator && !printRangeIndicator.classList.contains('hidden');
                if (wasVisible) {
                    printRangeIndicator.classList.add('hidden');
                }

                // 临时移除缩放变换以获得原始尺寸
                const originalTransform = documentContainer.style.transform;
                documentContainer.style.transform = 'scale(1)';

                if (typeof jsPDF !== 'undefined' && typeof html2canvas !== 'undefined') {
                    html2canvas(documentContainer, {
                        scale: 2, // 高质量输出
                        useCORS: true,
                        allowTaint: true,
                        backgroundColor: '#ffffff',
                        width: 794, // A4宽度
                        height: 1123, // A4高度
                        scrollX: 0,
                        scrollY: 0
                    }).then(canvas => {
                        const pdf = new jsPDF('p', 'mm', 'a4');
                        const imgData = canvas.toDataURL('image/png', 1.0); // 最高质量
                        
                        // A4尺寸：210mm x 297mm
                        const pdfWidth = 210;
                        const pdfHeight = 297;
                        
                        // 添加图片到PDF，确保完整覆盖A4页面
                        pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight, '', 'FAST');
                        
                        // 生成文件名（包含文档类型和时间戳）
                        const docType = getCurrentDocumentType() || 'document';
                        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
                        const filename = `${docType}_${timestamp}.pdf`;
                        
                        // 保存PDF文件
                        pdf.save(filename);
                        
                        pageLogger.info('IndexPage', 'exportAsPDF', '✅ PDF导出成功', { filename });
                        
                        // 显示成功提示
                        showExportSuccessNotification('PDF', filename);
                        
                        // 恢复UI状态
                        restoreExportUI();
                    }).catch(error => {
                        pageLogger.error('IndexPage', 'exportAsPDF', '❌ PDF导出失败', error);
                        alert('PDF导出失败：' + error.message);
                        restoreExportUI();
                    });
                } else {
                    // 简化模式提示
                    pageLogger.warn('IndexPage', 'exportAsPDF', '⚠️ jsPDF或html2canvas未加载，使用浏览器打印');
                    alert('PDF导出功能需要完整的jsPDF和html2canvas库。\n当前为简化模式，请使用浏览器的打印功能代替。');
                    
                    // 打开浏览器打印对话框
                    window.print();
                    restoreExportUI();
                }

                // 恢复UI状态的内部函数
                function restoreExportUI() {
                    // 恢复缩放变换
                    documentContainer.style.transform = originalTransform;
                    
                    // 恢复打印范围指示器
                    if (wasVisible && printRangeIndicator) {
                        printRangeIndicator.classList.remove('hidden');
                    }
                    
                    // 恢复按钮状态
                    if (exportBtn) {
                        exportBtn.textContent = originalText || '导出PDF';
                        exportBtn.disabled = false;
                    }
                }
            } catch (error) {
                pageLogger.error('IndexPage', 'exportAsPDF', '❌ PDF导出异常', error);
                alert('PDF导出失败：' + error.message);
            }
        }

        /**
         * @function 获取当前文档类型
         * @description 获取当前选中的文档类型，用于生成文件名
         */
        function getCurrentDocumentType() {
            const documentTypeSelect = document.getElementById('document-type');
            if (documentTypeSelect) {
                const selectedValue = documentTypeSelect.value;
                const typeMap = {
                    'receipt': '收据',
                    'invoice': '发票', 
                    'quotation': '报价单',
                    'driver-agreement': '司机协议'
                };
                return typeMap[selectedValue] || selectedValue;
            }
            return 'document';
        }

        /**
         * @function 显示导出成功通知
         * @description 显示导出成功的用户友好提示
         */
        function showExportSuccessNotification(type, filename) {
            // 创建成功提示元素
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300';
            notification.innerHTML = `
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                        <div class="font-semibold">${type}导出成功</div>
                        <div class="text-sm opacity-90">${filename}</div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // 3秒后自动移除
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        /**
         * @function 调整内容区域padding
         * @description 动态调整主内容区域的padding，确保内容不被页眉页脚遮挡
         * 根据页眉页脚容器的实际高度动态计算并设置padding值
         */
        function adjustContentPadding() {
            try {
                // 获取主内容包装器
                const mainContentWrapper = document.querySelector('.main-content-wrapper');
                if (!mainContentWrapper) {
                    pageLogger.warn('IndexPage', 'adjustContentPadding', '主内容包装器未找到');
                    return;
                }

                // 获取页眉容器
                const headerContainer = document.querySelector('.document-header-image-container');
                const footerContainer = document.querySelector('.unified-document-footer.company-footer-image-container');

                // 获取实际高度
                let headerHeight = 0;
                let footerHeight = 0;
                
                if (headerContainer) {
                    headerHeight = headerContainer.offsetHeight;
                }
                
                if (footerContainer) {
                    footerHeight = footerContainer.offsetHeight;
                }

                // 添加安全间距（避免内容紧贴页眉页脚）
                const safeGap = 10; // 10px安全间距
                
                // 使用CSS变量中定义的固定值，确保一致性
                const fixedHeaderHeight = 160; // 与CSS变量--fixed-header-height一致
                const fixedFooterHeight = 38;  // 与CSS变量--fixed-footer-height一致
                
                // 计算最终的padding值（优先使用固定值，确保一致性）
                const topPadding = headerHeight > 0 ? Math.max(headerHeight + safeGap, fixedHeaderHeight + safeGap) : fixedHeaderHeight + safeGap;
                const bottomPadding = footerHeight > 0 ? Math.max(footerHeight + safeGap, fixedFooterHeight + safeGap) : fixedFooterHeight + safeGap;

                // 应用padding到主内容包装器
                mainContentWrapper.style.paddingTop = `${topPadding}px`;
                mainContentWrapper.style.paddingBottom = `${bottomPadding}px`;

                // 更新CSS变量（供其他地方使用）
                document.documentElement.style.setProperty('--content-safe-top', `${topPadding}px`);
                document.documentElement.style.setProperty('--content-safe-bottom', `${bottomPadding}px`);

                pageLogger.debug('IndexPage', 'adjustContentPadding', '✅ 内容区域padding调整完成', {
                    headerHeight,
                    footerHeight,
                    topPadding,
                    bottomPadding
                });

            } catch (error) {
                pageLogger.error('IndexPage', 'adjustContentPadding', '❌ 内容区域padding调整失败', error);
            }
        }

        // 页面加载完成时调整padding
        document.addEventListener('DOMContentLoaded', () => {
            // 延迟执行，确保页眉页脚图片加载完成
            setTimeout(adjustContentPadding, 100);
        });

        // 窗口大小变化时调整padding
        window.addEventListener('resize', () => {
            adjustContentPadding();
        });

        // 暴露管理器到全局
        window.A4PreviewZoomManager = A4PreviewZoomManager;
    </script>
</body>
</html>
